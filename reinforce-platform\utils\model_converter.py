#!/usr/bin/env python3
"""
模型推理工具

此模块提供了模型推理的功能，支持使用OM模型进行图像推理（华为昇腾NPU加速）。
同时保持对传统YOLOv8模型的兼容性支持。
"""

import os
import logging
import paramiko

# 导入环境变量函数
try:
    from environ import Env
    env = Env()
except ImportError:
    # 如果没有安装django-environ，使用简单的环境变量读取
    def env(key, default=None):
        return os.environ.get(key, default)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelConverter:
    """
    模型推理工具类

    提供OM模型推理功能，支持华为昇腾NPU加速的远程执行和结果获取。
    同时保持对传统模型的兼容性支持。
    """
    
    def __init__(self, server_info=None):
        """
        初始化模型推理器

        Args:
            server_info: 服务器连接信息，包含以下字段：
                - ip: 服务器IP地址
                - port: SSH端口
                - username: 用户名，默认为'root'
                - password: 密码
        """
        self.server_info = server_info
        self.ssh_client = None
        self.remote_dir = "/workspace"
        
        # 如果提供了服务器信息，则自动连接
        if server_info:
            self.connect()
    
    def connect(self, server_info=None):
        """
        连接到远程服务器
        
        Args:
            server_info: 服务器连接信息，如果为None则使用初始化时提供的信息
            
        Returns:
            bool: 连接成功返回True，否则返回False
        """
        if server_info:
            self.server_info = server_info
            
        if not self.server_info:
            logger.error("未提供服务器连接信息")
            return False
            
        try:
            # 创建SSH客户端
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接远程服务器
            username = self.server_info.get('username', 'root')
            logger.info(f"正在连接服务器: {self.server_info['ip']}:{self.server_info['port']}")
            self.ssh_client.connect(
                hostname=self.server_info['ip'],
                port=int(self.server_info['port']),
                username=username,
                password=self.server_info['password'],
                timeout=30
            )
            logger.info("SSH连接成功")
            return True
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            self.ssh_client = None
            return False
    
    def disconnect(self):
        """
        断开与远程服务器的连接
        """
        if self.ssh_client:
            self.ssh_client.close()
            self.ssh_client = None
            logger.info("SSH连接已关闭")
    
    def _get_remote_script_path(self, script_name):
        """
        获取远程脚本路径
        
        Args:
            script_name: 脚本名称
            
        Returns:
            str: 远程脚本路径
        """
        return f"{self.remote_dir}/{script_name}"
    
    def upload_script(self, local_path, remote_name=None):
        """
        上传脚本到远程服务器
        
        Args:
            local_path: 本地脚本路径
            remote_name: 远程脚本名称，如果为None则使用本地文件名
            
        Returns:
            bool: 上传成功返回True，否则返回False
        """
        if not self.ssh_client:
            logger.error("SSH未连接，无法上传文件")
            return False
            
        try:
            # 确保远程工作目录存在
            mkdir_cmd = f"mkdir -p {self.remote_dir}"
            self.ssh_client.exec_command(mkdir_cmd)
            
            # 创建SFTP客户端
            sftp = self.ssh_client.open_sftp()
            
            # 确定远程文件名
            if not remote_name:
                remote_name = os.path.basename(local_path)
            
            # 上传文件
            remote_path = f"{self.remote_dir}/{remote_name}"
            logger.info(f"上传脚本: {local_path} -> {remote_path}")
            sftp.put(local_path, remote_path)
            
            # 关闭SFTP连接
            sftp.close()
            
            logger.info(f"脚本上传成功: {remote_name}")
            return True
        except Exception as e:
            logger.error(f"上传脚本失败: {e}")
            return False
    

    
    def run_inference(self, model_list, input_source, confidence_threshold=0.5, output_path=None, device_id=0):
        """
        运行多模型推理（单张图片）- 对所有模型进行推理

        Args:
            model_list: 模型列表，每个元素包含 {'om_model_path': '', 'pt_model_path': '', 'model_name': ''}
            input_source: 输入源 (图片路径或base64数据)
            confidence_threshold: 置信度阈值
            output_path: 输出路径 (可选)
            device_id: NPU设备ID

        Returns:
            dict: 多模型推理结果
        """
        try:
            if not self.ssh_client:
                return {"success": False, "error": "SSH未连接"}

            if not model_list or len(model_list) == 0:
                return {"success": False, "error": "未提供模型列表"}

            logger.info(f"🔍 开始多模型推理，模型数量: {len(model_list)}")

            # 获取远程脚本路径
            remote_script_path = self._get_remote_script_path("model_info_extractor.py")

            # 获取ULTRALYTICS_DIR用于--mount_path参数
            ultralytics_mount_path = env('ULTRALYTICS_DIR', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8')

            # 获取挂载路径
            mount_path = env('SITON_DATA_MOUNT_PATH', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9')

            # 设置环境变量命令
            set_env_cmd = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'

            # 处理输入源 - 如果是base64数据，先上传到远程服务器
            processed_input_source = input_source
            temp_file_created = False

            # 检查是否为base64数据（长度超过200字符且不是文件路径）
            if len(input_source) > 200:
                try:
                    import base64
                    import tempfile
                    import time

                    logger.info("检测到base64数据，正在上传到远程服务器...")

                    # 生成唯一的临时文件名
                    timestamp = int(time.time())
                    temp_filename = f"temp_input_{timestamp}.jpg"
                    remote_temp_path = f"{self.remote_dir}/{temp_filename}"
                    print(remote_temp_path)

                    # 处理base64数据
                    if input_source.startswith('data:image/'):
                        # 移除data:image/...;base64,前缀
                        _, encoded_data = input_source.split(',', 1)
                    else:
                        encoded_data = input_source

                    # 解码base64数据
                    image_data = base64.b64decode(encoded_data)
                    # 创建本地临时文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as local_temp:
                        local_temp.write(image_data)
                        local_temp_path = local_temp.name

                    # 上传到远程服务器
                    sftp = self.ssh_client.open_sftp()
                    sftp.put(local_temp_path, remote_temp_path)
                    sftp.close()

                    # 删除本地临时文件
                    os.unlink(local_temp_path)

                    # 使用远程临时文件路径
                    processed_input_source = remote_temp_path
                    temp_file_created = True

                    logger.info(f"Base64数据已上传到: {remote_temp_path}")

                except Exception as e:
                    logger.error(f"处理base64数据失败: {e}")
                    # 如果处理失败，仍然尝试直接使用原始数据
                    processed_input_source = input_source

            # 对每个模型进行推理
            model_results = []

            for i, model_info in enumerate(model_list):
                model_name = model_info.get('model_name', f'模型{i+1}')
                om_model_path = model_info.get('om_model_path')
                pt_model_path = model_info.get('pt_model_path')

                logger.info(f"🔄 开始推理模型 {i+1}/{len(model_list)}: {model_name}")

                # 验证模型路径
                if not om_model_path:
                    logger.error(f"❌ 模型 {model_name} 未提供OM模型路径")
                    model_results.append({
                        "model_name": model_name,
                        "success": False,
                        "error": "未提供OM模型路径"
                    })
                    continue

                if not pt_model_path:
                    logger.error(f"❌ 模型 {model_name} 未提供PT模型路径")
                    model_results.append({
                        "model_name": model_name,
                        "success": False,
                        "error": "未提供PT模型路径"
                    })
                    continue

                # 为每个模型创建独立的输出路径
                if output_path:
                    base_name = os.path.splitext(os.path.basename(output_path))[0]
                    ext = os.path.splitext(output_path)[1] or '.jpg'
                    model_output_path = os.path.join(os.path.dirname(output_path), f"{base_name}_{model_name}{ext}")
                else:
                    model_output_path = None

                # 执行单个模型的推理
                model_result = self._run_single_model_inference(
                    remote_script_path, om_model_path, pt_model_path,
                    processed_input_source, confidence_threshold,
                    model_output_path, device_id, set_env_cmd,
                    ultralytics_mount_path, mount_path, model_name
                )

                model_results.append(model_result)

            # 清理临时文件
            if temp_file_created and processed_input_source != input_source:
                try:
                    cleanup_cmd = f"rm -f {processed_input_source}"
                    self.ssh_client.exec_command(cleanup_cmd)
                    logger.info(f"已清理临时文件: {processed_input_source}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")

            # 计算整体统计信息
            successful_models = [r for r in model_results if r.get("success", False)]
            total_objects_detected = sum(r.get("detection_summary", {}).get("total_objects", 0) for r in successful_models)

            # 构建多模型推理结果
            return {
                "success": True,
                "model_results": model_results,
                "total_models": len(model_list),
                "successful_models": len(successful_models),
                "total_objects_detected": total_objects_detected,
                "inference_type": "multi_model"
            }

        except Exception as e:
            logger.error(f"多模型推理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 在异常情况下也要清理临时文件
            try:
                if 'temp_file_created' in locals() and temp_file_created and 'processed_input_source' in locals():
                    cleanup_cmd = f"rm -f {processed_input_source}"
                    self.ssh_client.exec_command(cleanup_cmd)
                    logger.info(f"异常情况下已清理临时文件: {processed_input_source}")
            except:
                pass  # 忽略清理过程中的错误

            return {"success": False, "error": f"多模型推理失败: {str(e)}"}

    def _run_single_model_inference(self, remote_script_path, om_model_path, pt_model_path,
                                   processed_input_source, confidence_threshold, output_path,
                                   device_id, set_env_cmd, ultralytics_mount_path, mount_path, model_name):
        """
        运行单个模型的推理

        Args:
            remote_script_path: 远程脚本路径
            om_model_path: OM模型路径
            pt_model_path: PT模型路径
            processed_input_source: 处理后的输入源
            confidence_threshold: 置信度阈值
            output_path: 输出路径
            device_id: 设备ID
            set_env_cmd: 环境变量设置命令
            ultralytics_mount_path: ultralytics挂载路径
            mount_path: 挂载路径
            model_name: 模型名称

        Returns:
            dict: 单个模型的推理结果
        """
        try:
            # 构建OM推理命令
            python_cmd = f"python {remote_script_path} om_infer --om_model {om_model_path} --pt_model {pt_model_path} --image {processed_input_source} --conf {confidence_threshold} --device_id {device_id} --ultralytics_mount_path {ultralytics_mount_path} --mount_path {mount_path}"

            # 如果有输出路径，添加到Python命令中
            if output_path:
                python_cmd += f" --save_path {output_path}"

            infer_cmd = f"""
cd {self.remote_dir} && \
echo "开始{model_name}OM推理..." && \
echo "OM模型路径: {om_model_path}" && \
echo "PT模型路径: {pt_model_path}" && \
echo "输入图片: {processed_input_source}" && \
echo "置信度阈值: {confidence_threshold}" && \
echo "设备ID: {device_id}" && \
{python_cmd}
"""

            # 组合完整命令
            full_cmd = f"bash -c 'set -e && {set_env_cmd} && {infer_cmd}'"

            # 显示处理后的输入源信息
            display_input = processed_input_source if len(processed_input_source) < 100 else f"{processed_input_source[:50]}...(truncated)"
            logger.info(f"🔍 开始{model_name}OM模型推理: {om_model_path} -> {display_input}")

            # 执行推理命令
            _, stdout, stderr = self.ssh_client.exec_command(full_cmd, timeout=300)
            output = stdout.read().decode('utf-8', errors='ignore')
            error = stderr.read().decode('utf-8', errors='ignore')

            # 检查错误
            critical_errors = ["错误", "Error", "Exception", "Traceback", "Failed", "失败"]
            has_critical_error = any(err_word in error for err_word in critical_errors) and "warning" not in error.lower()

            if has_critical_error:
                logger.error(f"❌ {model_name}OM模型推理失败: {error}")
                return {
                    "model_name": model_name,
                    "success": False,
                    "error": error,
                    "inference_log": output
                }

            # 解析推理结果
            result_img_path = None
            detections = []
            detection_count = 0

            # 解析输出
            output_lines = output.split('\n')
            for line in output_lines:
                line = line.strip()

                # 解析结果路径
                if any(keyword in line for keyword in ["推理完成，结果保存在", "OM推理完成，结果保存在"]):
                    try:
                        result_img_path = line.split(": ")[-1].strip()
                        logger.info(f"✅ {model_name}结果路径: {result_img_path}")
                    except Exception as e:
                        logger.warning(f"{model_name}解析结果路径失败: {e}")

                elif "INFERENCE_RESULT_PATH:" in line:
                    try:
                        result_img_path = line.split("INFERENCE_RESULT_PATH:")[-1].strip()
                        logger.info(f"✅ {model_name}特殊标记路径: {result_img_path}")
                    except Exception as e:
                        logger.warning(f"{model_name}解析特殊标记失败: {e}")

                # 解析检测数量
                elif "检测到" in line and "个目标" in line:
                    try:
                        import re
                        match = re.search(r'检测到\s*(\d+)\s*个目标', line)
                        if match:
                            detection_count = int(match.group(1))
                            logger.info(f"🎯 {model_name}检测到目标数量: {detection_count}")
                    except Exception as e:
                        logger.warning(f"{model_name}解析检测数量失败: {e}")

                # 解析具体检测结果
                elif "目标" in line and "类别=" in line:
                    try:
                        if ": " in line:
                            result_part = line.split(": ", 1)[1]
                            parts = result_part.split(", ")

                            cls_name = None
                            for part in parts:
                                if "类别=" in part:
                                    cls_name = part.split("=", 1)[1].strip()
                                    break

                            if cls_name:
                                detections.append({"class": cls_name})
                                logger.debug(f"📍 {model_name}检测到: {cls_name}")

                    except Exception as e:
                        logger.warning(f"{model_name}解析检测结果失败: {e}")

            # 获取图片数据
            image_data = None
            if result_img_path:
                try:
                    sftp = self.ssh_client.open_sftp()
                    import tempfile
                    import base64

                    with tempfile.NamedTemporaryFile(delete=False) as temp:
                        temp_path = temp.name
                        sftp.get(result_img_path, temp_path)

                    with open(temp_path, 'rb') as f:
                        image_bytes = f.read()
                        image_data = base64.b64encode(image_bytes).decode('utf-8')

                    os.unlink(temp_path)
                    sftp.close()
                    logger.info(f"{model_name}图片数据获取成功")
                except Exception as e:
                    logger.error(f"{model_name}获取图片数据失败: {e}")

            # 统计类别信息
            class_counts = {}
            for detection in detections:
                class_name = detection.get("class", "unknown")
                class_counts[class_name] = class_counts.get(class_name, 0) + 1

            # 构建检测统计信息
            detection_summary = {
                "total_objects": detection_count,
                "class_counts": class_counts,
                "class_details": []
            }

            for class_name, count in class_counts.items():
                detection_summary["class_details"].append({
                    "class_name": class_name,
                    "count": count
                })

            return {
                "model_name": model_name,
                "success": True,
                "result_path": result_img_path,
                "detection_count": detection_count,
                "class_counts": class_counts,
                "detection_summary": detection_summary,
                "inference_log": output,
                "image_data": image_data,
                "image_format": "base64"
            }

        except Exception as e:
            logger.error(f"{model_name}推理失败: {e}")
            return {
                "model_name": model_name,
                "success": False,
                "error": f"推理失败: {str(e)}"
            }

    def _run_batch_multi_model_inference(self, model_list, input_sources, confidence_threshold, output_dir, device_id):
        """
        运行批量多模型推理

        Args:
            model_list: 模型列表
            input_sources: 输入源列表
            confidence_threshold: 置信度阈值
            output_dir: 输出目录
            device_id: 设备ID

        Returns:
            dict: 批量多模型推理结果
        """
        try:
            logger.info(f"🔄 开始批量多模型推理: {len(input_sources)} 张图片, {len(model_list)} 个模型")

            # 获取远程脚本路径
            remote_script_path = self._get_remote_script_path("model_info_extractor.py")

            # 获取环境变量
            ultralytics_mount_path = env('ULTRALYTICS_DIR', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8')
            mount_path = env('SITON_DATA_MOUNT_PATH', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9')
            set_env_cmd = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'

            # 处理输入源列表
            processed_input_sources = []
            temp_files_created = []

            for i, input_source in enumerate(input_sources):
                processed_input_source = input_source
                temp_file_created = False

                # 检查是否为base64数据
                if len(input_source) > 200:
                    try:
                        import base64
                        import tempfile
                        import time

                        logger.info(f"处理第 {i+1} 张图片的base64数据...")

                        # 生成唯一的临时文件名
                        timestamp = int(time.time())
                        temp_filename = f"temp_batch_{timestamp}_{i}.jpg"
                        remote_temp_path = f"{self.remote_dir}/{temp_filename}"

                        # 处理base64数据
                        if input_source.startswith('data:image/'):
                            _, encoded_data = input_source.split(',', 1)
                        else:
                            encoded_data = input_source

                        # 解码base64数据
                        image_data = base64.b64decode(encoded_data)
                        # 创建本地临时文件
                        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as local_temp:
                            local_temp.write(image_data)
                            local_temp_path = local_temp.name

                        # 上传到远程服务器
                        sftp = self.ssh_client.open_sftp()
                        sftp.put(local_temp_path, remote_temp_path)
                        sftp.close()

                        # 删除本地临时文件
                        os.unlink(local_temp_path)

                        # 使用远程临时文件路径
                        processed_input_source = remote_temp_path
                        temp_file_created = True
                        temp_files_created.append(remote_temp_path)

                        logger.info(f"第 {i+1} 张图片已上传到: {remote_temp_path}")

                    except Exception as e:
                        logger.error(f"处理第 {i+1} 张图片的base64数据失败: {e}")
                        processed_input_source = input_source

                processed_input_sources.append(processed_input_source)

            # 对每个模型进行批量推理
            model_results = []

            for model_info in model_list:
                model_name = model_info.get('model_name', '未知模型')
                logger.info(f"🔄 开始模型 {model_name} 的批量推理")

                # 为每个模型执行批量推理
                model_result = self._run_single_model_batch_inference(
                    remote_script_path, model_info, processed_input_sources,
                    confidence_threshold, output_dir, device_id, set_env_cmd,
                    ultralytics_mount_path, mount_path
                )

                # 处理返回结果，可能是单个结果或结果列表
                if isinstance(model_result, list):
                    # 如果返回的是列表，添加所有结果
                    model_results.extend(model_result)
                else:
                    # 如果返回的是单个结果，直接添加
                    model_results.append(model_result)

            # 清理临时文件
            for temp_file in temp_files_created:
                try:
                    cleanup_cmd = f"rm -f {temp_file}"
                    self.ssh_client.exec_command(cleanup_cmd)
                    logger.info(f"已清理临时文件: {temp_file}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {e}")

            # 计算整体统计信息
            successful_models = [r for r in model_results if r.get("success", False)]

            # 计算总检测数量
            total_objects_detected = sum(
                r.get("detection_count", 0) for r in successful_models
            )

            # 计算实际的模型数量（考虑到每个模型可能产生多个结果）
            unique_model_names = set()
            for r in model_results:
                if r.get("success", False):
                    model_name = r.get("model_name", "")
                    # 移除 "_image_X" 后缀来获取原始模型名
                    base_model_name = model_name.split("_image_")[0] if "_image_" in model_name else model_name
                    unique_model_names.add(base_model_name)

            # 构建批量多模型推理结果 - 统一使用multi_model格式
            return {
                "success": True,
                "model_results": model_results,
                "total_models": len(model_list),
                "successful_models": len(unique_model_names),
                "total_objects_detected": total_objects_detected,
                "total_images": len(input_sources),
                "processed_images": len(processed_input_sources),
                "inference_type": "multi_model"  # 统一使用multi_model，前端根据图片数量判断是否批量
            }

        except Exception as e:
            logger.error(f"批量多模型推理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 在异常情况下也要清理临时文件
            try:
                if 'temp_files_created' in locals():
                    for temp_file in temp_files_created:
                        cleanup_cmd = f"rm -f {temp_file}"
                        self.ssh_client.exec_command(cleanup_cmd)
                        logger.info(f"异常情况下已清理临时文件: {temp_file}")
            except:
                pass

            return {"success": False, "error": f"批量多模型推理失败: {str(e)}"}

    def _run_single_model_batch_inference(self, remote_script_path, model_info, input_sources,
                                         confidence_threshold, output_dir, device_id, set_env_cmd,
                                         ultralytics_mount_path, mount_path):
        """
        运行单个模型的批量推理

        Args:
            remote_script_path: 远程脚本路径
            model_info: 模型信息
            input_sources: 输入源列表
            confidence_threshold: 置信度阈值
            output_dir: 输出目录
            device_id: 设备ID
            set_env_cmd: 环境变量设置命令
            ultralytics_mount_path: ultralytics挂载路径
            mount_path: 挂载路径

        Returns:
            dict: 单个模型的批量推理结果
        """
        try:
            model_name = model_info.get('model_name', '未知模型')
            om_model_path = model_info.get('om_model_path')
            pt_model_path = model_info.get('pt_model_path')

            # 构建输入源字符串
            input_sources_str = ",".join(input_sources)

            # 为每个模型创建独立的输出目录
            if output_dir:
                model_output_dir = f"{output_dir}_{model_name}"
            else:
                model_output_dir = None

            # 构建批量推理命令
            python_cmd = f"python {remote_script_path} om_batch_infer --om_model {om_model_path} --pt_model {pt_model_path} --images {input_sources_str} --conf {confidence_threshold} --device_id {device_id} --ultralytics_mount_path {ultralytics_mount_path} --mount_path {mount_path}"

            if model_output_dir:
                python_cmd += f" --save_dir {model_output_dir}"

            batch_infer_cmd = f"""
cd {self.remote_dir} && \
echo "开始{model_name}批量OM推理..." && \
echo "OM模型路径: {om_model_path}" && \
echo "PT模型路径: {pt_model_path}" && \
echo "输入图片数量: {len(input_sources)}" && \
echo "置信度阈值: {confidence_threshold}" && \
echo "设备ID: {device_id}" && \
{python_cmd}
"""

            # 组合完整命令
            full_cmd = f"bash -c 'set -e && {set_env_cmd} && {batch_infer_cmd}'"

            logger.info(f"🔍 开始{model_name}批量OM模型推理: {len(input_sources)} 张图片")

            # 执行批量推理命令
            _, stdout, stderr = self.ssh_client.exec_command(full_cmd, timeout=600)  # 10分钟超时
            output = stdout.read().decode('utf-8', errors='ignore')
            error = stderr.read().decode('utf-8', errors='ignore')

            # 检查错误
            critical_errors = ["错误", "Error", "Exception", "Traceback", "Failed", "失败"]
            has_critical_error = any(err_word in error for err_word in critical_errors) and "warning" not in error.lower()

            if has_critical_error:
                logger.error(f"❌ {model_name}批量OM模型推理失败: {error}")
                return {
                    "model_name": model_name,
                    "success": False,
                    "error": error,
                    "inference_log": output
                }

            # 解析批量推理结果 - 参考单张推理的格式
            total_detections = 0
            overall_class_counts = {}

            # 解析输出获取检测结果
            output_lines = output.split('\n')
            for line in output_lines:
                line = line.strip()

                # 解析检测数量
                if "检测到" in line and "个目标" in line:
                    try:
                        import re
                        match = re.search(r'检测到\s*(\d+)\s*个目标', line)
                        if match:
                            detection_count = int(match.group(1))
                            total_detections += detection_count
                            logger.info(f"🎯 {model_name}检测到目标数量: {detection_count}")
                    except Exception as e:
                        logger.warning(f"{model_name}解析检测数量失败: {e}")

                # 解析具体检测结果
                elif "目标" in line and "类别=" in line:
                    try:
                        if ": " in line:
                            result_part = line.split(": ", 1)[1]
                            parts = result_part.split(", ")

                            cls_name = None
                            for part in parts:
                                if "类别=" in part:
                                    cls_name = part.split("=", 1)[1].strip()
                                    break

                            if cls_name:
                                overall_class_counts[cls_name] = overall_class_counts.get(cls_name, 0) + 1
                                logger.debug(f"📍 {model_name}检测到: {cls_name}")

                    except Exception as e:
                        logger.warning(f"{model_name}解析检测结果失败: {e}")

            # 获取所有图片的结果路径（批量推理会有多个结果）
            result_img_paths = []
            for line in output_lines:
                if any(keyword in line for keyword in ["推理完成，结果保存在", "OM推理完成，结果保存在"]):
                    try:
                        result_img_path = line.split(": ")[-1].strip()
                        result_img_paths.append(result_img_path)
                        logger.info(f"✅ {model_name}结果路径: {result_img_path}")
                    except Exception as e:
                        logger.warning(f"{model_name}解析结果路径失败: {e}")

            # 获取所有图片数据
            image_data_list = []
            if result_img_paths:
                try:
                    sftp = self.ssh_client.open_sftp()
                    import tempfile
                    import base64

                    for i, result_img_path in enumerate(result_img_paths):
                        try:
                            with tempfile.NamedTemporaryFile(delete=False) as temp:
                                temp_path = temp.name
                                sftp.get(result_img_path, temp_path)

                            with open(temp_path, 'rb') as f:
                                image_bytes = f.read()
                                image_data = base64.b64encode(image_bytes).decode('utf-8')
                                image_data_list.append(image_data)

                            os.unlink(temp_path)
                            logger.info(f"{model_name}第{i+1}张图片数据获取成功")
                        except Exception as e:
                            logger.error(f"{model_name}获取第{i+1}张图片数据失败: {e}")
                            image_data_list.append(None)

                    sftp.close()
                    logger.info(f"{model_name}共获取到{len(image_data_list)}张图片数据")
                except Exception as e:
                    logger.error(f"{model_name}获取图片数据失败: {e}")
                    image_data_list = []

            # 构建检测统计信息
            detection_summary = {
                "total_objects": total_detections,
                "class_counts": overall_class_counts,
                "class_details": []
            }

            for class_name, count in overall_class_counts.items():
                detection_summary["class_details"].append({
                    "class_name": class_name,
                    "count": count
                })

            logger.info(f"✅ {model_name}批量推理完成，总检测数: {total_detections}")

            # 为批量推理返回多个结果，每张图片一个结果
            if len(image_data_list) > 1:
                # 多张图片的情况，返回每张图片的单独结果
                results = []
                images_per_result = len(image_data_list)
                detections_per_image = total_detections // images_per_result if images_per_result > 0 else 0

                for i, image_data in enumerate(image_data_list):
                    if image_data:  # 只处理成功获取的图片数据
                        result = {
                            "model_name": f"{model_name}_image_{i+1}",
                            "success": True,
                            "result_path": result_img_paths[i] if i < len(result_img_paths) else None,
                            "detection_count": detections_per_image,  # 平均分配检测数量
                            "class_counts": overall_class_counts,  # 使用整体的类别统计
                            "detection_summary": detection_summary,
                            "inference_log": output,
                            "image_data": image_data,
                            "image_format": "base64"
                        }
                        results.append(result)

                return results
            else:
                # 单张图片或没有图片数据的情况，返回单个结果
                return {
                    "model_name": model_name,
                    "success": True,
                    "result_path": result_img_paths[0] if result_img_paths else None,
                    "detection_count": total_detections,
                    "class_counts": overall_class_counts,
                    "detection_summary": detection_summary,
                    "inference_log": output,
                    "image_data": image_data_list[0] if image_data_list else None,
                    "image_format": "base64"
                }

        except Exception as e:
            logger.error(f"{model_name}批量推理失败: {e}")
            return {
                "model_name": model_name,
                "success": False,
                "error": f"批量推理失败: {str(e)}"
            }

    def run_batch_inference(self, model_list=None, input_sources=None, confidence_threshold=0.5, output_dir=None, device_id=0, **kwargs):
        """
        运行批量模型推理（多张图片）- 支持多模型推理

        Args:
            model_list: 模型列表，每个元素包含 {'om_model_path': '', 'pt_model_path': '', 'model_name': ''}
            input_sources: 输入源列表，每个元素可以是图片路径或base64数据
            confidence_threshold: 置信度阈值
            output_dir: 输出目录 (可选)
            device_id: NPU设备ID
            **kwargs: 兼容性参数（model_path, om_model_path, pt_model_path）

        Returns:
            dict: 批量推理结果
        """
        try:
            if not self.ssh_client:
                return {"success": False, "error": "SSH未连接"}

            if not input_sources or len(input_sources) == 0:
                return {"success": False, "error": "输入源列表为空"}

            # 支持多模型推理
            if model_list:
                logger.info(f"🔍 开始批量多模型推理: {len(input_sources)} 张图片, {len(model_list)} 个模型")
                return self._run_batch_multi_model_inference(model_list, input_sources, confidence_threshold, output_dir, device_id)

            # 兼容性支持：单模型批量推理
            model_path = kwargs.get('model_path')
            om_model_path = kwargs.get('om_model_path')
            pt_model_path = kwargs.get('pt_model_path')

            if not om_model_path and not model_path:
                return {"success": False, "error": "未提供模型路径"}

            logger.info(f"🔍 开始单模型批量推理: {len(input_sources)} 张图片")

            # 获取远程脚本路径
            remote_script_path = self._get_remote_script_path("model_info_extractor.py")

            # 获取ULTRALYTICS_DIR用于--mount_path参数
            ultralytics_mount_path = env('ULTRALYTICS_DIR', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9/data/ultralytics_v8')

            # 获取挂载路径
            mount_path = env('SITON_DATA_MOUNT_PATH', default='/root/siton-data-b496463103254f46976c4ff88ea74bc9')

            # 设置环境变量命令
            set_env_cmd = 'source ~/.bashrc && source /usr/local/Ascend/ascend-toolkit/set_env.sh && source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh && export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH'

            # 处理输入源列表 - 上传base64数据到远程服务器
            processed_input_sources = []
            temp_files_created = []

            for i, input_source in enumerate(input_sources):
                processed_input_source = input_source
                temp_file_created = False

                # 检查是否为base64数据
                if len(input_source) > 200:
                    try:
                        import base64
                        import tempfile
                        import time

                        logger.info(f"处理第 {i+1} 张图片的base64数据...")

                        # 生成唯一的临时文件名
                        timestamp = int(time.time())
                        temp_filename = f"temp_batch_input_{timestamp}_{i}.jpg"
                        remote_temp_path = f"{self.remote_dir}/{temp_filename}"

                        # 处理base64数据
                        if input_source.startswith('data:image/'):
                            # 移除data:image/...;base64,前缀
                            _, encoded_data = input_source.split(',', 1)
                        else:
                            encoded_data = input_source

                        # 解码base64数据
                        image_data = base64.b64decode(encoded_data)
                        # 创建本地临时文件
                        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as local_temp:
                            local_temp.write(image_data)
                            local_temp_path = local_temp.name

                        # 上传到远程服务器
                        sftp = self.ssh_client.open_sftp()
                        sftp.put(local_temp_path, remote_temp_path)
                        sftp.close()

                        # 删除本地临时文件
                        os.unlink(local_temp_path)

                        # 使用远程临时文件路径
                        processed_input_source = remote_temp_path
                        temp_file_created = True

                        logger.info(f"第 {i+1} 张图片已上传到: {remote_temp_path}")

                    except Exception as e:
                        logger.error(f"处理第 {i+1} 张图片的base64数据失败: {e}")
                        # 如果处理失败，仍然尝试直接使用原始数据
                        processed_input_source = input_source

                processed_input_sources.append(processed_input_source)
                temp_files_created.append(temp_file_created)

            # 将输入源列表转换为以逗号分隔的字符串
            input_sources_str = ",".join(processed_input_sources)

            # 确定使用的模型路径
            actual_om_model = om_model_path if om_model_path else model_path
            actual_pt_model = pt_model_path if pt_model_path else model_path

            # 验证模型路径
            if not actual_om_model:
                logger.error("❌ 未提供OM模型路径")
                return {"success": False, "error": "未提供OM模型路径"}

            if not actual_pt_model:
                logger.error("❌ 未提供PT模型路径")
                return {"success": False, "error": "未提供PT模型路径"}

            # 构建OM批量推理命令，添加更多调试信息
            # 构建Python命令参数
            python_cmd = f"python {remote_script_path} om_batch_infer --om_model {actual_om_model} --pt_model {actual_pt_model} --images \"{input_sources_str}\" --conf {confidence_threshold} --device_id {device_id} --ultralytics_mount_path {ultralytics_mount_path} --mount_path {mount_path}"

            # 如果有输出目录，添加到Python命令中
            if output_dir:
                python_cmd += f" --output_dir {output_dir}"

            batch_infer_cmd = f"""
cd {self.remote_dir} && \
echo "开始OM批量推理..." && \
echo "OM模型路径: {actual_om_model}" && \
echo "PT模型路径: {actual_pt_model}" && \
echo "图片数量: {len(input_sources)}" && \
echo "置信度阈值: {confidence_threshold}" && \
echo "设备ID: {device_id}" && \
{python_cmd}
"""

            # 组合完整命令，添加环境变量设置和错误处理
            full_cmd = f"bash -c 'set -e && {set_env_cmd} && {batch_infer_cmd}'"
            logger.info(f"🔍 执行OM批量推理: {len(input_sources)} 张图片")
            logger.info(f"📋 批量推理参数: 置信度={confidence_threshold}, 设备ID={device_id}")

            # 执行批量推理命令
            try:
                _, stdout, stderr = self.ssh_client.exec_command(full_cmd, timeout=600)  # 10分钟超时
                output = stdout.read().decode('utf-8', errors='ignore')
                error = stderr.read().decode('utf-8', errors='ignore')

                # 记录详细的输出信息
                logger.info(f"📤 批量推理命令输出长度: {len(output)} 字符")
                if error:
                    logger.warning(f"⚠️ 批量推理过程中的警告/错误信息: {error}")

            except Exception as e:
                logger.error(f"❌ 执行批量推理命令失败: {e}")
                return {
                    "success": False,
                    "error": f"执行批量推理命令失败: {str(e)}",
                    "inference_log": ""
                }

            # 改进错误检测逻辑
            critical_errors = ["错误", "Error", "Exception", "Traceback", "Failed", "失败"]
            has_critical_error = any(err_word in error for err_word in critical_errors) and "warning" not in error.lower()

            if has_critical_error:
                logger.error(f"❌ OM批量推理失败: {error}")
                return {
                    "success": False,
                    "error": error,
                    "inference_log": output
                }

            # 解析批量推理结果
            batch_results = self._parse_batch_inference_output(output)

            # 获取结果图片数据并完善检测统计信息
            for result in batch_results:
                # 为每张图片添加详细的检测统计信息
                class_counts = result.get("class_counts", {})
                detection_count = result.get("detection_count", 0)

                detection_summary = {
                    "total_objects": detection_count,
                    "class_counts": class_counts,
                    "class_details": []
                }

                # 为前端提供更友好的类别详情
                for class_name, count in class_counts.items():
                    detection_summary["class_details"].append({
                        "class_name": class_name,
                        "count": count
                    })

                result["detection_summary"] = detection_summary

                if result.get("result_path"):
                    try:
                        # 获取图片的base64数据
                        image_data = self._get_remote_image_data(result["result_path"])
                        result["image_data"] = image_data
                        result["image_format"] = "base64"
                    except Exception as e:
                        logger.error(f"获取结果图片数据失败: {e}")
                        result["image_data"] = None

            # 清理临时文件
            self._cleanup_temp_files(processed_input_sources, temp_files_created)

            logger.info(f"✅ 批量推理完成，处理了 {len(batch_results)} 张图片")

            # 计算整体统计信息
            total_objects_detected = sum(result.get("detection_count", 0) for result in batch_results)
            overall_class_counts = {}

            for result in batch_results:
                class_counts = result.get("class_counts", {})
                for class_name, count in class_counts.items():
                    overall_class_counts[class_name] = overall_class_counts.get(class_name, 0) + count

            # 构建整体检测统计信息
            overall_detection_summary = {
                "total_images": len(input_sources),
                "processed_images": len(batch_results),
                "total_objects_detected": total_objects_detected,
                "overall_class_counts": overall_class_counts,
                "overall_class_details": []
            }

            # 为前端提供更友好的整体类别详情
            for class_name, count in overall_class_counts.items():
                overall_detection_summary["overall_class_details"].append({
                    "class_name": class_name,
                    "count": count
                })

            return {
                "success": True,
                "results": batch_results,
                "total_images": len(input_sources),  # 保持向后兼容
                "processed_images": len(batch_results),  # 保持向后兼容
                "overall_detection_summary": overall_detection_summary,  # 新增：整体检测统计信息
                "inference_log": output
            }

        except Exception as e:
            logger.error(f"批量模型推理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # 在异常情况下也要清理临时文件
            try:
                if 'temp_files_created' in locals() and 'processed_input_sources' in locals():
                    self._cleanup_temp_files(processed_input_sources, temp_files_created)
            except:
                pass  # 忽略清理过程中的错误

            return {"success": False, "error": f"批量模型推理失败: {str(e)}"}

    def _parse_batch_inference_output(self, output):
        """
        解析批量推理输出结果（改进版）

        Args:
            output: 推理命令的输出

        Returns:
            list: 解析后的结果列表
        """
        results = []
        current_result = {}

        # 只显示输出的前2000字符，避免日志过长
        display_output = output[:2000] + "..." if len(output) > 2000 else output
        logger.info(f"📋 解析批量推理输出 ({len(output)} 字符):")
        logger.info(display_output)

        output_lines = output.split('\n')
        for line in output_lines:
            line = line.strip()

            # 检测新图片处理开始
            if any(marker in line for marker in ["=== 处理图片", "=== 使用OM模型处理图片"]):
                # 保存上一个结果
                if current_result:
                    results.append(current_result)
                    logger.debug(f"✅ 完成图片 {current_result.get('image_index', '?')} 的解析")

                # 开始新的结果
                current_result = {
                    "image_index": len(results),
                    "detections": [],
                    "detection_count": 0,
                    "result_path": None,
                    "success": False
                }
                logger.debug(f"🔄 开始解析图片 {current_result['image_index']}")

            # 解析结果路径的多种格式
            elif any(keyword in line for keyword in ["推理完成，结果保存在", "OM推理完成，结果保存在"]):
                try:
                    result_path = line.split(": ")[-1].strip()
                    current_result["result_path"] = result_path
                    current_result["success"] = True
                    logger.debug(f"✅ 图片 {current_result.get('image_index', '?')} 结果路径: {result_path}")
                except Exception as e:
                    logger.warning(f"解析结果路径失败: {line}, 错误: {e}")

            elif "BATCH_INFERENCE_RESULT_PATH:" in line:
                try:
                    result_path = line.split("BATCH_INFERENCE_RESULT_PATH:")[-1].strip()
                    current_result["result_path"] = result_path
                    current_result["success"] = True
                    logger.debug(f"✅ 图片 {current_result.get('image_index', '?')} 特殊标记路径: {result_path}")
                except Exception as e:
                    logger.warning(f"解析特殊标记失败: {line}, 错误: {e}")

            # 解析检测数量
            elif "检测到" in line and "个目标" in line:
                try:
                    import re
                    match = re.search(r'检测到\s*(\d+)\s*个目标', line)
                    if match:
                        detection_count = int(match.group(1))
                        current_result["detection_count"] = detection_count
                        logger.debug(f"🎯 图片 {current_result.get('image_index', '?')} 检测到 {detection_count} 个目标")
                except Exception as e:
                    logger.warning(f"解析检测数量失败: {line}, 错误: {e}")

            # 解析具体检测结果 - 只统计类别数量
            elif "目标" in line and "类别=" in line:
                try:
                    if ": " in line:
                        result_part = line.split(": ", 1)[1]
                        parts = result_part.split(", ")

                        cls_name = None
                        for part in parts:
                            if "类别=" in part:
                                cls_name = part.split("=", 1)[1].strip()
                                break  # 找到类别就退出循环

                        # 只要有类别名称就记录
                        if cls_name:
                            # 初始化类别统计字典
                            if "class_counts" not in current_result:
                                current_result["class_counts"] = {}

                            # 统计每个类别的数量
                            if cls_name in current_result["class_counts"]:
                                current_result["class_counts"][cls_name] += 1
                            else:
                                current_result["class_counts"][cls_name] = 1

                            # 保存简化的检测信息
                            current_result["detections"].append({"class": cls_name})

                            logger.debug(f"📍 图片 {current_result.get('image_index', '?')} 检测到: {cls_name}")

                except Exception as e:
                    logger.warning(f"解析检测结果行失败: {line}, 错误: {e}")

            # 检查错误信息
            elif any(err in line.lower() for err in ["error", "exception", "failed", "错误", "失败"]):
                if "warning" not in line.lower() and current_result:
                    current_result["error"] = line
                    logger.warning(f"⚠️ 图片 {current_result.get('image_index', '?')} 发现错误: {line}")

        # 添加最后一个结果
        if current_result:
            results.append(current_result)
            logger.debug(f"✅ 完成最后一张图片 {current_result.get('image_index', '?')} 的解析")

        logger.info(f"📊 批量推理解析完成: 总共 {len(results)} 张图片")
        successful_count = sum(1 for r in results if r.get("success", False))
        logger.info(f"📈 成功处理: {successful_count}/{len(results)} 张图片")

        return results

    def _get_remote_image_data(self, remote_path):
        """
        获取远程图片的base64数据（改进版）

        Args:
            remote_path: 远程图片路径

        Returns:
            str: base64编码的图片数据，失败时返回None
        """
        if not remote_path:
            logger.warning("远程图片路径为空")
            return None

        try:
            # 创建SFTP客户端
            sftp = self.ssh_client.open_sftp()

            # 处理路径，确保正确的远程路径
            if not remote_path.startswith("/workspace/"):
                remote_path = "/workspace/" + remote_path.lstrip("/")

            # 首先检查文件是否存在
            try:
                file_stat = sftp.stat(remote_path)
                logger.debug(f"📁 远程文件信息: {remote_path}, 大小: {file_stat.st_size} 字节")
            except FileNotFoundError:
                logger.error(f"❌ 远程文件不存在: {remote_path}")
                sftp.close()
                return None

            # 创建临时文件保存图片
            import tempfile
            import base64

            with tempfile.NamedTemporaryFile(delete=False) as temp:
                temp_path = temp.name
                logger.debug(f"📥 下载推理结果图片: {remote_path} -> {temp_path}")

                try:
                    sftp.get(remote_path, temp_path)
                except Exception as download_error:
                    logger.error(f"❌ 下载文件失败: {download_error}")
                    sftp.close()
                    os.unlink(temp_path)
                    return None

            # 验证下载的文件
            if not os.path.exists(temp_path) or os.path.getsize(temp_path) == 0:
                logger.error(f"❌ 下载的文件无效: {temp_path}")
                sftp.close()
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                return None

            # 读取图片数据并转为base64
            try:
                with open(temp_path, 'rb') as f:
                    image_bytes = f.read()
                    if len(image_bytes) == 0:
                        logger.error("❌ 图片文件为空")
                        return None

                    image_data = base64.b64encode(image_bytes).decode('utf-8')

                logger.info(f"✅ 图片数据获取成功，大小: {len(image_data) / 1024:.2f} KB")

            except Exception as encode_error:
                logger.error(f"❌ 图片编码失败: {encode_error}")
                return None
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                # 关闭SFTP连接
                sftp.close()

            return image_data

        except Exception as e:
            logger.error(f"❌ 获取远程图片数据失败: {e}")
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return None

    def _cleanup_temp_files(self, processed_input_sources, temp_files_created):
        """
        清理临时文件

        Args:
            processed_input_sources: 处理后的输入源列表
            temp_files_created: 临时文件创建标志列表
        """
        for i, (source, is_temp) in enumerate(zip(processed_input_sources, temp_files_created)):
            if is_temp:
                try:
                    cleanup_cmd = f"rm -f {source}"
                    self.ssh_client.exec_command(cleanup_cmd)
                    logger.info(f"已清理第 {i+1} 张图片的临时文件: {source}")
                except Exception as e:
                    logger.warning(f"清理第 {i+1} 张图片的临时文件失败: {e}")