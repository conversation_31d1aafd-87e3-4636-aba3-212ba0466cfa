"""
训练模型管理相关的视图
"""

import logging
import json
import threading
from pathlib import Path
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
import time
import os

from backend_api.models.training_model import TrainingModel, ModelConversionLog, ModelInferenceLog
from backend_api.models.training import TrainingTask
from backend_api.serializers.training_model import (
    TrainingModelSerializer,
    TrainingModelCreateSerializer,
    TrainingModelUpdateSerializer,
    ModelConversionLogSerializer,
    ModelInferenceLogSerializer,

    ModelInferenceRequestSerializer,
    ModelBatchInferenceRequestSerializer,
    ModelListFilterSerializer,
    TrainingModelDetailSerializer
)
from utils.yolov8_docker_trainer import YOLOv8DockerTrainer
from utils.model_converter import ModelConverter

logger = logging.getLogger(__name__)


def async_model_inference(model_id, input_source, confidence_threshold, iou_threshold, max_detections, save_result, return_image, inference_log_id=None):
    """异步执行模型推理任务"""
    inference_log = None
    try:
        # 获取模型
        model = TrainingModel.objects.get(id=model_id)

        logger.info(f"开始异步推理模型 {model.model_name}，输入源: {input_source[:50] if len(input_source) > 50 else input_source}...")

        # 如果需要保存结果，获取已创建的推理日志
        if save_result and inference_log_id:
            try:
                inference_log = ModelInferenceLog.objects.get(id=inference_log_id)
                logger.info(f"使用已创建的推理日志: {inference_log_id}")
            except ModelInferenceLog.DoesNotExist:
                logger.error(f"推理日志ID {inference_log_id} 不存在")
                return

        # 获取模型的服务器连接信息
        server_ip = model.server_ip
        server_port = model.server_port
        server_password = model.server_password

        if not server_ip or not server_port or not server_password:
            # 如果模型没有服务器信息，尝试从训练任务中获取
            if hasattr(model, 'task') and model.task:
                task = model.task
                if hasattr(task, 'server_ip') and task.server_ip:
                    server_ip = task.server_ip
                    server_port = task.server_port
                    server_password = task.server_password
                else:
                    other_models = TrainingModel.objects.filter(task=task).exclude(id=model.id)
                    for other_model in other_models:
                        if other_model.server_ip and other_model.server_port and other_model.server_password:
                            server_ip = other_model.server_ip
                            server_port = other_model.server_port
                            server_password = other_model.server_password
                            break

                    if not server_ip or not server_port or not server_password:
                        raise ValueError("无法获取服务器连接信息，请确保模型或训练任务中包含服务器信息")

        if not server_ip or not server_port or not server_password:
            raise ValueError("无法获取服务器连接信息")

        # 创建模型推理器
        server_info = {
            'ip': server_ip,
            'port': server_port,
            'password': server_password,
            'username': 'root'
        }

        logger.info(f"连接到服务器 {server_ip}:{server_port} 进行模型推理")
        converter = ModelConverter(server_info)

        # 上传推理脚本（在推理之前）
        script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "utils", "model_info_extractor.py")
        if not os.path.exists(script_path):
            logger.warning(f"推理脚本不存在: {script_path}，尝试使用相对路径")
            script_path = os.path.join("utils", "model_info_extractor.py")
            if not os.path.exists(script_path):
                logger.error(f"推理脚本不存在: {script_path}")
                raise FileNotFoundError(f"推理脚本不存在: utils/model_info_extractor.py")

        logger.info(f"上传推理脚本: {script_path}")
        converter.upload_script(script_path, "model_info_extractor.py")

        # 构建输出路径
        output_path = None
        if save_result:
            timestamp = int(time.time())
            output_filename = f"inference_{model.id}_{timestamp}.jpg"
            output_path = f"/workspace/{output_filename}"

        # 执行OM推理
        inference_result = converter.run_inference(
            model_path=model.converted_model_path,  # 兼容性保留
            input_source=input_source,
            confidence_threshold=confidence_threshold,
            output_path=output_path,
            om_model_path=model.converted_model_path,  # OM模型路径
            pt_model_path=model.model_path,  # PT模型路径（用于获取类别名称）
            device_id=0  # NPU设备ID，可以根据需要配置
        )
        # 关闭连接
        converter.disconnect()

        # 处理推理结果
        if inference_result.get('success'):
            result_data = {
                'success': True,
                'detections': inference_result.get('detections', []),
                'detection_count': inference_result.get('detection_count', 0),
                'inference_time_ms': inference_result.get('inference_time_ms', 0.0),
                'confidence_threshold': confidence_threshold,
                'iou_threshold': iou_threshold,
                'max_detections': max_detections,
                'result_path': inference_result.get('result_path'),
                'inference_log': inference_result.get('inference_log', ''),
                'status': 'completed',
                'message': '推理任务已完成'
            }

            # 如果需要返回图片数据
            if return_image:
                if inference_result.get('image_data'):
                    result_data['image_data'] = inference_result.get('image_data')
                    result_data['image_format'] = inference_result.get('image_format', 'base64')
                    result_data['has_image'] = True
                else:
                    result_data['image_data'] = None
                    result_data['image_format'] = None
                    result_data['has_image'] = False
                    result_data['image_message'] = inference_result.get('message', '图片数据不可用')

            # 更新推理日志
            if save_result and inference_log:
                inference_log.inference_time_ms = result_data.get('inference_time_ms', 0.0)
                inference_log.detections_count = len(result_data.get('detections', []))
                inference_log.result_data = result_data
                inference_log.output_path = result_data.get('result_path')
                inference_log.save()

            logger.info(f"✅ 模型推理成功，检测到 {result_data.get('detection_count', 0)} 个目标")
        else:
            error_msg = inference_result.get('error', '推理失败')
            logger.error(f"❌ 模型推理失败: {error_msg}")

            # 更新推理日志
            if save_result and inference_log:
                inference_log.result_data = {
                    'success': False,
                    'detections': [],
                    'detection_count': 0,
                    'inference_time_ms': 0.0,
                    'confidence_threshold': confidence_threshold,
                    'iou_threshold': iou_threshold,
                    'max_detections': max_detections,
                    'result_path': '',
                    'status': 'failed',
                    'message': error_msg,
                    'error': error_msg,
                    'inference_log': inference_result.get('inference_log', ''),
                    'image_data': None,
                    'image_format': None,
                    'has_image': False,
                    'image_message': '推理失败，无法生成结果图片'
                }
                inference_log.save()

            raise Exception(error_msg)

    except Exception as e:
        logger.error(f"异步推理过程中发生异常: {e}", exc_info=True)

        # 更新推理日志
        if save_result and inference_log:
            try:
                inference_log.result_data = {
                    'success': False,
                    'detections': [],
                    'detection_count': 0,
                    'inference_time_ms': 0.0,
                    'confidence_threshold': confidence_threshold,
                    'iou_threshold': iou_threshold,
                    'max_detections': max_detections,
                    'result_path': '',
                    'status': 'failed',
                    'message': f'推理过程中发生异常: {str(e)}',
                    'error': str(e),
                    'inference_log': '',
                    'image_data': None,
                    'image_format': None,
                    'has_image': False,
                    'image_message': '推理失败，无法生成结果图片'
                }
                inference_log.save()
            except Exception as update_error:
                logger.error(f"更新推理日志失败: {update_error}")


class TrainingModelListView(APIView):
    """训练模型列表视图"""
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取训练模型列表"""
        try:
            # 验证查询参数
            filter_serializer = ModelListFilterSerializer(data=request.query_params)
            if not filter_serializer.is_valid():
                return Response({
                    'success': False,
                    'message': '查询参数无效',
                    'errors': filter_serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            filters = filter_serializer.validated_data
            
            # 构建查询
            queryset = TrainingModel.objects.all()
            
            # 应用过滤条件
            if 'task_id' in filters:
                queryset = queryset.filter(task_id=filters['task_id'])
            
            if 'export_status' in filters:
                queryset = queryset.filter(conversion_status=filters['export_status'])
                
            # 按是否已转换过滤
            # if 'is_converted' in filters:
            #     is_converted = filters['is_converted']
            #     queryset = queryset.filter(is_converted=is_converted)
            
            if 'architecture' in filters:
                queryset = queryset.filter(architecture__icontains=filters['architecture'])
            
            if 'min_accuracy' in filters:
                queryset = queryset.filter(accuracy__gte=filters['min_accuracy'])
            
            if 'max_model_size' in filters:
                queryset = queryset.filter(model_size_mb__lte=filters['max_model_size'])
            
            if 'search' in filters:
                search_term = filters['search']
                queryset = queryset.filter(
                    Q(model_name__icontains=search_term) |
                    Q(notes__icontains=search_term) |
                    Q(architecture__icontains=search_term)
                )
            
            # 分页
            page = filters.get('page', 1)
            page_size = filters.get('page_size', 20)
            paginator = Paginator(queryset.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = TrainingModelSerializer(page_obj.object_list, many=True)
            
            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': paginator.num_pages,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取训练模型列表失败: {e}")
            return Response({
                'success': False,
                'message': f'获取模型列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingModelDetailView(APIView):
    """训练模型详情视图"""
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request, model_id):
        """获取模型详情"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            serializer = TrainingModelSerializer(model)
            return Response({
                'success': True,
                'data': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取模型详情失败: {e}")
            return Response({
                'success': False,
                'message': f'获取模型详情失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, model_id):
        """更新模型信息"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            serializer = TrainingModelUpdateSerializer(model, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                
                # 返回更新后的数据
                response_serializer = TrainingModelSerializer(model)
                return Response({
                    'success': True,
                    'message': '模型信息更新成功',
                    'data': response_serializer.data
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'message': '更新参数无效',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"更新模型信息失败: {e}")
            return Response({
                'success': False,
                'message': f'更新模型信息失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def delete(self, request, model_id):
        """删除模型"""
        try:
            model = get_object_or_404(
                TrainingModel,
                id=model_id,
                created_by=request.user
            )
            
            model_name = model.model_name
            model.delete()
            
            return Response({
                'success': True,
                'message': f'模型 {model_name} 删除成功'
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"删除模型失败: {e}")
            return Response({
                'success': False,
                'message': f'删除模型失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingModelCreateView(APIView):
    """创建训练模型视图"""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """创建新的训练模型记录"""
        try:
            serializer = TrainingModelCreateSerializer(data=request.data)
            if serializer.is_valid():
                # 生成模型名称：任务ID + 模型文件名
                task = serializer.validated_data.get('task')
                model_path = serializer.validated_data.get('model_path')

                if task and model_path:
                    model_filename = Path(model_path).stem
                    model_name = f"task_{task.id}_{model_filename}"
                else:
                    # 如果没有提供task_id或model_path，使用默认名称
                    model_name = f"model_{int(timezone.now().timestamp())}"

                # 创建模型记录
                model = serializer.save(
                    created_by=request.user,
                    model_name=model_name
                )
                
                # 返回创建的模型信息
                response_serializer = TrainingModelSerializer(model)
                return Response({
                    'success': True,
                    'message': '训练模型创建成功',
                    'data': response_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'success': False,
                    'message': '创建参数无效',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            logger.error(f"创建训练模型失败: {e}")
            return Response({
                'success': False,
                'message': f'创建训练模型失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelInferenceView(APIView):
    """模型推理视图"""

    permission_classes = [IsAuthenticated]

    def get(self, request):
        """查询模型推理状态"""
        try:
            inference_log_id = request.query_params.get('inference_log_id')
            model_id = request.query_params.get('model_id')

            if inference_log_id:
                # 根据推理日志ID查询
                try:
                    inference_log = ModelInferenceLog.objects.get(id=inference_log_id)
                    model = inference_log.training_model
                except ModelInferenceLog.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'推理日志ID {inference_log_id} 不存在',
                        'status': 'not_found'
                    }, status=status.HTTP_404_NOT_FOUND)
            elif model_id:
                # 根据模型ID查询最新的推理记录
                try:
                    model = TrainingModel.objects.get(id=model_id)
                    inference_log = ModelInferenceLog.objects.filter(
                        training_model=model
                    ).order_by('-created_time').first()

                    if not inference_log:
                        return Response({
                            'success': False,
                            'message': f'模型ID {model_id} 没有推理记录',
                            'status': 'no_inference'
                        }, status=status.HTTP_404_NOT_FOUND)
                except TrainingModel.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'模型ID {model_id} 不存在',
                        'status': 'not_found'
                    }, status=status.HTTP_404_NOT_FOUND)
            else:
                return Response({
                    'success': False,
                    'message': '请提供 model_id 或 inference_log_id 参数',
                    'status': 'bad_request'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 构建响应数据，保持与POST接口一致的结构
            response_data = {
                'success': True,
                'data': {
                    'inference_log_id': inference_log.id,
                    'model_id': model.id,
                    'model_name': model.model_name,
                    'result': inference_log.result_data or {
                        'detections': [],
                        'detection_count': 0,
                        'inference_time_ms': 0.0,
                        'status': 'processing',
                        'message': '推理任务正在处理中'
                    }
                },
                'status': 'completed'
            }

            # 判断推理状态
            if inference_log.result_data and inference_log.result_data.get('success'):
                response_data['message'] = '推理任务已完成'
            elif inference_log.result_data and not inference_log.result_data.get('success'):
                response_data['message'] = '推理任务失败'
                response_data['status'] = 'failed'
            else:
                response_data['message'] = '推理任务正在处理中'
                response_data['status'] = 'processing'

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"查询推理状态失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'message': f'查询推理状态失败: {str(e)}',
                'status': 'error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """使用模型进行推理（异步处理）"""
        try:
            # 记录请求数据，帮助调试
            # logger.info(f"收到模型推理请求: {request.data}")

            # 检查是否为多模型推理请求（通过task_id）
            task_id = request.data.get('task_id')
            if task_id:
                # 多模型推理请求：通过task_id获取该任务下的所有模型
                return self._handle_multi_model_inference_by_task(request.data)

            # # 检查是否为传统的多模型推理请求（通过model_list）
            # model_list = request.data.get('model_list')
            # if model_list:
            #     # 多模型推理请求
            #     return self._handle_multi_model_inference(request.data)

            # serializer = ModelInferenceRequestSerializer(data=request.data)
            # if serializer.is_valid():
            #     model_id = serializer.validated_data['model_id']
            #     input_source = serializer.validated_data.get('input_source')
            #     input_sources = serializer.validated_data.get('input_sources')
            #     confidence_threshold = serializer.validated_data.get('confidence_threshold', 0.5)
            #     iou_threshold = serializer.validated_data.get('iou_threshold', 0.45)
            #     max_detections = serializer.validated_data.get('max_detections', 1000)
            #     save_result = serializer.validated_data.get('save_result', True)
            #     return_image = serializer.validated_data.get('return_image', True)

            #     # 判断是单张还是批量推理
            #     if input_sources and len(input_sources) > 1:
            #         # 批量推理
            #         logger.info(f"开始处理批量推理请求: model_id={model_id}, 图片数量={len(input_sources)}")
            #         return self._handle_batch_inference(
            #             model_id, input_sources, confidence_threshold,
            #             iou_threshold, max_detections, save_result, return_image
            #         )
            #     elif input_source:
            #         # 单张推理
            #         display_input = input_source if len(input_source) < 100 else f"{input_source[:50]}...(truncated)"
            #         logger.info(f"开始处理单张推理请求: model_id={model_id}, input={display_input}")
            #         return self._handle_single_inference(
            #             model_id, input_source, confidence_threshold,
            #             iou_threshold, max_detections, save_result, return_image
            #         )
            #     elif input_sources and len(input_sources) == 1:
            #         # 只有一张图片，使用单张推理
            #         display_input = input_sources[0] if len(input_sources[0]) < 100 else f"{input_sources[0][:50]}...(truncated)"
            #         logger.info(f"开始处理单张推理请求(来自数组): model_id={model_id}, input={display_input}")
            #         return self._handle_single_inference(
            #             model_id, input_sources[0], confidence_threshold,
            #             iou_threshold, max_detections, save_result, return_image
            #         )

            #     # 如果到这里，说明验证有问题
            #     return Response({
            #         'success': False,
            #         'message': '推理参数验证失败',
            #         'status': 'failed'
            #     }, status=status.HTTP_400_BAD_REQUEST)
            # else:
            #     logger.warning(f"模型推理参数验证失败: {serializer.errors}")
            #     return Response({
            #         'success': False,
            #         'message': '推理参数无效',
            #         'errors': serializer.errors,
            #         'status': 'failed'
            #     }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"模型推理失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'message': f'模型推理失败: {str(e)}',
                'status': 'failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _handle_single_inference(self, model_id, input_source, confidence_threshold, iou_threshold, max_detections, save_result, return_image):
        """处理单张推理"""
        try:
            # 获取模型
            try:
                model = TrainingModel.objects.get(id=model_id)
                logger.info(f"找到模型: {model.model_name}, 路径: {model.converted_model_path}")
            except TrainingModel.DoesNotExist:
                logger.error(f"模型ID {model_id} 不存在")
                return Response({
                    'success': False,
                    'message': f'模型ID {model_id} 不存在',
                    'status': 'failed'
                }, status=status.HTTP_404_NOT_FOUND)

            # 如果需要保存结果，先创建推理日志占位
            inference_log = None
            if save_result:
                inference_log = ModelInferenceLog.objects.create(
                    training_model=model,
                    input_source=input_source,
                    confidence_threshold=confidence_threshold,
                    created_by=self.request.user,
                    result_data={'status': 'processing', 'message': '推理任务已启动，正在处理中'}
                )

            # 启动异步推理任务
            inference_thread = threading.Thread(
                target=async_model_inference,
                args=(model_id, input_source, confidence_threshold, iou_threshold, max_detections, save_result, return_image),
                kwargs={'inference_log_id': inference_log.id if inference_log else None},
                daemon=True
            )
            inference_thread.start()

            logger.info(f"✅ 单张推理任务已启动")

            # 构建兼容前端的响应数据结构
            response_data = {
                'model_id': model.id,
                'model_name': model.model_name,
                'result': {
                    'detections': [],
                    'detection_count': 0,
                    'inference_time_ms': 0.0,
                    'confidence_threshold': confidence_threshold,
                    'iou_threshold': iou_threshold,
                    'max_detections': max_detections,
                    'result_path': '',
                    'status': 'processing',
                    'message': '推理任务正在后台处理中，请稍后查询结果'
                }
            }

            # 如果前端期望返回图片，提供占位符
            if return_image:
                response_data['result']['has_image'] = False
                response_data['result']['image_format'] = None
                response_data['result']['image_message'] = '推理正在进行中，图片数据将在完成后提供'

            if save_result and inference_log:
                response_data['inference_log_id'] = inference_log.id

            # 立即返回处理中状态
            return Response({
                'success': True,
                'message': '推理任务已启动，正在后台处理中',
                'data': response_data,
                'status': 'processing'
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            logger.error(f"单张推理失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'message': f'单张推理失败: {str(e)}',
                'status': 'failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _handle_batch_inference(self, model_id, input_sources, confidence_threshold, iou_threshold, max_detections, save_result, return_image):
        """处理批量推理"""
        try:
            # 获取模型
            try:
                model = TrainingModel.objects.get(id=model_id)
                logger.info(f"找到模型: {model.model_name}, 路径: {model.converted_model_path}")
            except TrainingModel.DoesNotExist:
                logger.error(f"模型ID {model_id} 不存在")
                return Response({
                    'success': False,
                    'message': f'模型ID {model_id} 不存在',
                    'status': 'failed'
                }, status=status.HTTP_404_NOT_FOUND)

            # 如果需要保存结果，先创建推理日志占位
            inference_log = None
            if save_result:
                # 对于批量推理，input_source字段存储图片数量信息
                input_source_info = f"批量推理 - {len(input_sources)} 张图片"
                inference_log = ModelInferenceLog.objects.create(
                    training_model=model,
                    input_source=input_source_info,
                    confidence_threshold=confidence_threshold,
                    created_by=self.request.user,
                    result_data={'status': 'processing', 'message': f'批量推理任务已启动，正在处理 {len(input_sources)} 张图片'}
                )

            # 启动异步批量推理任务
            batch_inference_thread = threading.Thread(
                target=async_batch_model_inference,
                args=(model_id, input_sources, confidence_threshold, iou_threshold, max_detections, save_result, return_image),
                kwargs={'inference_log_id': inference_log.id if inference_log else None},
                daemon=True
            )
            batch_inference_thread.start()

            logger.info(f"✅ 批量推理任务已启动")

            # 构建兼容前端的响应数据结构
            response_data = {
                'model_id': model.id,
                'model_name': model.model_name,
                'result': {
                    'batch_results': [],
                    'total_images': len(input_sources),
                    'processed_images': 0,
                    'confidence_threshold': confidence_threshold,
                    'iou_threshold': iou_threshold,
                    'max_detections': max_detections,
                    'status': 'processing',
                    'message': f'批量推理任务正在后台处理中，共 {len(input_sources)} 张图片，请稍后查询结果'
                }
            }

            # 如果前端期望返回图片，提供占位符
            if return_image:
                response_data['result']['has_images'] = False
                response_data['result']['image_format'] = None
                response_data['result']['image_message'] = '批量推理正在进行中，图片数据将在完成后提供'

            if save_result and inference_log:
                response_data['inference_log_id'] = inference_log.id

            # 立即返回处理中状态
            return Response({
                'success': True,
                'message': f'批量推理任务已启动，正在后台处理 {len(input_sources)} 张图片',
                'data': response_data,
                'status': 'processing'
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            logger.error(f"批量推理失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'message': f'批量推理失败: {str(e)}',
                'status': 'failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # def _handle_multi_model_inference(self, request_data):
    #     """处理多模型推理请求"""
    #     try:
    #         model_list = request_data.get('model_list', [])
    #         input_source = request_data.get('input_source')
    #         input_sources = request_data.get('input_sources')
    #         confidence_threshold = request_data.get('confidence_threshold', 0.5)
    #         device_id = request_data.get('device_id', 0)

    #         if not model_list:
    #             return Response({
    #                 'success': False,
    #                 'error': '未提供模型列表'
    #             }, status=status.HTTP_400_BAD_REQUEST)

    #         logger.info(f"开始处理多模型推理请求，模型数量: {len(model_list)}")

    #         # 验证模型列表格式
    #         validated_models = []
    #         for model_info in model_list:
    #             if not isinstance(model_info, dict):
    #                 return Response({
    #                     'success': False,
    #                     'error': '模型信息格式错误'
    #                 }, status=status.HTTP_400_BAD_REQUEST)

    #             model_name = model_info.get('model_name')
    #             om_model_path = model_info.get('om_model_path')
    #             pt_model_path = model_info.get('pt_model_path')

    #             if not all([model_name, om_model_path, pt_model_path]):
    #                 return Response({
    #                     'success': False,
    #                     'error': f'模型 {model_name} 信息不完整'
    #                 }, status=status.HTTP_400_BAD_REQUEST)

    #             validated_models.append({
    #                 'model_name': model_name,
    #                 'om_model_path': om_model_path,
    #                 'pt_model_path': pt_model_path
    #             })

    #         # 判断是单张还是批量推理
    #         if input_sources and len(input_sources) > 1:
    #             # 批量多模型推理
    #             return self._handle_batch_multi_model_inference(
    #                 validated_models, input_sources, confidence_threshold, device_id
    #             )
    #         elif input_source:
    #             # 单张多模型推理
    #             return self._handle_single_multi_model_inference(
    #                 validated_models, input_source, confidence_threshold, device_id
    #             )
    #         elif input_sources and len(input_sources) == 1:
    #             # 单张多模型推理（来自数组）
    #             return self._handle_single_multi_model_inference(
    #                 validated_models, input_sources[0], confidence_threshold, device_id
    #             )
    #         else:
    #             return Response({
    #                 'success': False,
    #                 'error': '未提供输入图片'
    #             }, status=status.HTTP_400_BAD_REQUEST)

    #     except Exception as e:
    #         logger.error(f"多模型推理请求处理失败: {e}", exc_info=True)
    #         return Response({
    #             'success': False,
    #             'error': f'多模型推理请求处理失败: {str(e)}'
    #         }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _handle_multi_model_inference_by_task(self, request_data):
        """通过task_id处理多模型推理请求"""
        try:
            task_id = request_data.get('task_id')
            input_source = request_data.get('input_source')
            input_sources = request_data.get('input_sources')
            confidence_threshold = request_data.get('confidence_threshold', 0.5)
            device_id = request_data.get('device_id', 0)

            if not task_id:
                return Response({
                    'success': False,
                    'error': '未提供task_id'
                }, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"开始处理基于task_id的多模型推理请求，task_id: {task_id}")

            # 通过task_id获取该任务下的所有模型
            try:
                from backend_api.models.training import TrainingTask
                task = TrainingTask.objects.get(id=task_id)
                models = TrainingModel.objects.filter(task=task)

                if not models.exists():
                    return Response({
                        'success': False,
                        'error': f'任务 {task_id} 下没有找到已转换的模型'
                    }, status=status.HTTP_404_NOT_FOUND)

                # 构建模型列表
                model_list = []
                for model in models:
                    if model.converted_model_path and model.model_path:
                        model_list.append({
                            'model_name': model.model_name or f'Model_{model.id}',
                            'om_model_path': model.converted_model_path,
                            'pt_model_path': model.model_path,
                            'model_id': model.id
                        })

                if not model_list:
                    return Response({
                        'success': False,
                        'error': f'任务 {task_id} 下没有找到有效的模型路径'
                    }, status=status.HTTP_404_NOT_FOUND)

                logger.info(f"找到 {len(model_list)} 个可用模型: {[m['model_name'] for m in model_list]}")

                # 判断是单张还是批量推理
                if input_sources and len(input_sources) > 1:
                    # 批量多模型推理
                    return self._handle_batch_multi_model_inference(
                        model_list, input_sources, confidence_threshold, device_id
                    )
                elif input_source:
                    # 单张多模型推理
                    return self._handle_single_multi_model_inference(
                        model_list, input_source, confidence_threshold, device_id
                    )
                elif input_sources and len(input_sources) == 1:
                    print("单张多模型推理。。。")
                    # 单张多模型推理（来自数组）
                    return self._handle_single_multi_model_inference(
                        model_list, input_sources[0], confidence_threshold, device_id
                    )
                else:
                    return Response({
                        'success': False,
                        'error': '未提供输入图片'
                    }, status=status.HTTP_400_BAD_REQUEST)

            except TrainingTask.DoesNotExist:
                return Response({
                    'success': False,
                    'error': f'未找到task_id为 {task_id} 的训练任务'
                }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"基于task_id的多模型推理请求处理失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': f'多模型推理请求处理失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _handle_single_multi_model_inference(self, model_list, input_source, confidence_threshold, device_id):
        """处理单张图片多模型推理"""
        try:
            from utils.model_converter import ModelConverter
            from django.conf import settings
            import os

            if not model_list:
                return Response({
                    'success': False,
                    'error': '模型列表为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取第一个模型的数据库记录以获取服务器信息
            first_model_id = model_list[0].get('model_id')
            if not first_model_id:
                return Response({
                    'success': False,
                    'error': '模型信息中缺少model_id'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                first_model = TrainingModel.objects.get(id=first_model_id)
            except TrainingModel.DoesNotExist:
                return Response({
                    'success': False,
                    'error': f'未找到ID为 {first_model_id} 的模型'
                }, status=status.HTTP_404_NOT_FOUND)

            # 检查服务器连接信息
            if not all([first_model.server_ip, first_model.server_port, first_model.server_password]):
                return Response({
                    'success': False,
                    'error': '模型缺少完整的服务器连接信息'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 构建服务器连接信息
            server_info = {
                'ip': first_model.server_ip,
                'port': first_model.server_port,
                'username': 'root',  # 默认用户名
                'password': first_model.server_password
            }

            logger.info(f"使用服务器信息进行单张多模型推理: {server_info['ip']}:{server_info['port']}")

            # 创建模型转换器并传入服务器信息
            converter = ModelConverter(server_info=server_info)

            # 连接到远程服务器
            if not converter.connect():
                return Response({
                    'success': False,
                    'error': '无法连接到远程服务器'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # 上传推理脚本
            script_path = os.path.join(settings.BASE_DIR, 'utils', 'model_info_extractor.py')
            converter.upload_script(script_path, "model_info_extractor.py")

            # 执行多模型推理
            result = converter.run_inference(
                model_list=model_list,
                input_source=input_source,
                confidence_threshold=confidence_threshold,
                device_id=device_id
            )

            # 关闭连接
            converter.disconnect()

            if result.get('success'):
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'error': result.get('error', '多模型推理失败')
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"单张多模型推理失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': f'单张多模型推理失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _handle_batch_multi_model_inference(self, model_list, input_sources, confidence_threshold, device_id):
        """处理批量图片多模型推理"""
        try:
            from utils.model_converter import ModelConverter
            from django.conf import settings
            import os

            if not model_list:
                return Response({
                    'success': False,
                    'error': '模型列表为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取第一个模型的数据库记录以获取服务器信息
            first_model_id = model_list[0].get('model_id')
            if not first_model_id:
                return Response({
                    'success': False,
                    'error': '模型信息中缺少model_id'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                first_model = TrainingModel.objects.get(id=first_model_id)
            except TrainingModel.DoesNotExist:
                return Response({
                    'success': False,
                    'error': f'未找到ID为 {first_model_id} 的模型'
                }, status=status.HTTP_404_NOT_FOUND)

            # 检查服务器连接信息
            if not all([first_model.server_ip, first_model.server_port, first_model.server_password]):
                return Response({
                    'success': False,
                    'error': '模型缺少完整的服务器连接信息'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 构建服务器连接信息
            server_info = {
                'ip': first_model.server_ip,
                'port': first_model.server_port,
                'username': 'root',  # 默认用户名
                'password': first_model.server_password
            }

            logger.info(f"使用服务器信息进行多模型推理: {server_info['ip']}:{server_info['port']}")

            # 创建模型转换器并传入服务器信息
            converter = ModelConverter(server_info=server_info)

            # 连接到远程服务器
            if not converter.connect():
                return Response({
                    'success': False,
                    'error': '无法连接到远程服务器'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # 上传推理脚本
            script_path = os.path.join(settings.BASE_DIR, 'utils', 'model_info_extractor.py')
            converter.upload_script(script_path, "model_info_extractor.py")

            # 执行批量多模型推理
            result = converter.run_batch_inference(
                model_list=model_list,
                input_sources=input_sources,
                confidence_threshold=confidence_threshold,
                device_id=device_id
            )

            # 关闭连接
            converter.disconnect()

            if result.get('success'):
                return Response(result, status=status.HTTP_200_OK)
            else:
                return Response({
                    'success': False,
                    'error': result.get('error', '批量多模型推理失败')
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"批量多模型推理失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'error': f'批量多模型推理失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def async_batch_model_inference(model_id, input_sources, confidence_threshold, iou_threshold, max_detections, save_result, return_image, inference_log_id=None):
    """异步执行批量模型推理任务"""
    inference_log = None
    try:
        # 获取模型
        model = TrainingModel.objects.get(id=model_id)

        logger.info(f"开始异步批量推理模型 {model.model_name}，输入源数量: {len(input_sources)}")

        # 如果需要保存结果，获取已创建的推理日志
        if save_result and inference_log_id:
            try:
                inference_log = ModelInferenceLog.objects.get(id=inference_log_id)
                logger.info(f"使用已创建的推理日志: {inference_log_id}")
            except ModelInferenceLog.DoesNotExist:
                logger.error(f"推理日志ID {inference_log_id} 不存在")
                return

        # 获取模型的服务器连接信息
        server_ip = model.server_ip
        server_port = model.server_port
        server_password = model.server_password

        if not server_ip or not server_port or not server_password:
            # 如果模型没有服务器信息，尝试从训练任务中获取
            if hasattr(model, 'task') and model.task:
                task = model.task
                if hasattr(task, 'server_ip') and task.server_ip:
                    server_ip = task.server_ip
                    server_port = task.server_port
                    server_password = task.server_password
                else:
                    other_models = TrainingModel.objects.filter(task=task).exclude(id=model.id)
                    for other_model in other_models:
                        if other_model.server_ip and other_model.server_port and other_model.server_password:
                            server_ip = other_model.server_ip
                            server_port = other_model.server_port
                            server_password = other_model.server_password
                            break

                    if not server_ip or not server_port or not server_password:
                        raise ValueError("无法获取服务器连接信息，请确保模型或训练任务中包含服务器信息")

        if not server_ip or not server_port or not server_password:
            raise ValueError("无法获取服务器连接信息")

        # 创建模型转换器
        server_info = {
            'ip': server_ip,
            'port': server_port,
            'password': server_password,
            'username': 'root'
        }

        logger.info(f"连接到服务器 {server_ip}:{server_port} 进行批量模型推理")
        converter = ModelConverter(server_info)

        # 上传推理脚本（在推理之前）
        script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "utils", "model_info_extractor.py")
        if not os.path.exists(script_path):
            logger.warning(f"推理脚本不存在: {script_path}，尝试使用相对路径")
            script_path = os.path.join("utils", "model_info_extractor.py")
            if not os.path.exists(script_path):
                logger.error(f"推理脚本不存在: {script_path}")
                raise FileNotFoundError(f"推理脚本不存在: utils/model_info_extractor.py")

        logger.info(f"上传推理脚本: {script_path}")
        converter.upload_script(script_path, "model_info_extractor.py")

        # 构建输出目录
        output_dir = None
        if save_result:
            timestamp = int(time.time())
            output_dir = f"/workspace/batch_inference_{model.id}_{timestamp}"

        # 执行OM批量推理
        batch_inference_result = converter.run_batch_inference(
            model_path=model.converted_model_path,  # 兼容性保留
            input_sources=input_sources,
            confidence_threshold=confidence_threshold,
            output_dir=output_dir,
            om_model_path=model.converted_model_path,  # OM模型路径
            pt_model_path=model.model_path,  # PT模型路径（用于获取类别名称）
            device_id=0  # NPU设备ID，可以根据需要配置
        )

        # 关闭连接
        converter.disconnect()

        # 处理批量推理结果
        if batch_inference_result.get('success'):
            result_data = {
                'success': True,
                'batch_results': batch_inference_result.get('results', []),
                'total_images': batch_inference_result.get('total_images', 0),
                'processed_images': batch_inference_result.get('processed_images', 0),
                'confidence_threshold': confidence_threshold,
                'iou_threshold': iou_threshold,
                'max_detections': max_detections,
                'inference_log': batch_inference_result.get('inference_log', ''),
                'status': 'completed',
                'message': f'批量推理任务已完成，成功处理 {batch_inference_result.get("processed_images", 0)} / {batch_inference_result.get("total_images", 0)} 张图片'
            }

            # 如果需要返回图片数据，数据已经在batch_results中
            if return_image:
                result_data['has_images'] = True
                result_data['image_format'] = 'base64'
            else:
                # 如果不需要返回图片，移除图片数据以节省空间
                for result in result_data['batch_results']:
                    if 'image_data' in result:
                        del result['image_data']
                result_data['has_images'] = False

            # 更新推理日志
            if save_result and inference_log:
                inference_log.inference_time_ms = 0.0  # 批量推理时间暂时设为0
                inference_log.detections_count = sum(len(r.get('detections', [])) for r in result_data['batch_results'])
                inference_log.result_data = result_data
                inference_log.output_path = output_dir
                inference_log.save()

            logger.info(f"✅ 批量模型推理成功，处理了 {result_data.get('processed_images', 0)} 张图片")
        else:
            error_msg = batch_inference_result.get('error', '批量推理失败')
            logger.error(f"❌ 批量模型推理失败: {error_msg}")

            # 更新推理日志
            if save_result and inference_log:
                inference_log.result_data = {
                    'success': False,
                    'batch_results': [],
                    'total_images': len(input_sources),
                    'processed_images': 0,
                    'confidence_threshold': confidence_threshold,
                    'iou_threshold': iou_threshold,
                    'max_detections': max_detections,
                    'status': 'failed',
                    'message': error_msg,
                    'error': error_msg,
                    'inference_log': batch_inference_result.get('inference_log', ''),
                    'has_images': False,
                    'image_format': None
                }
                inference_log.save()

            raise Exception(error_msg)

    except Exception as e:
        logger.error(f"异步批量推理过程中发生异常: {e}", exc_info=True)

        # 更新推理日志
        if save_result and inference_log:
            try:
                inference_log.result_data = {
                    'success': False,
                    'batch_results': [],
                    'total_images': len(input_sources) if 'input_sources' in locals() else 0,
                    'processed_images': 0,
                    'confidence_threshold': confidence_threshold,
                    'iou_threshold': iou_threshold,
                    'max_detections': max_detections,
                    'status': 'failed',
                    'message': f'批量推理过程中发生异常: {str(e)}',
                    'error': str(e),
                    'inference_log': '',
                    'has_images': False,
                    'image_format': None
                }
                inference_log.save()
            except Exception as update_error:
                logger.error(f"更新推理日志失败: {update_error}")



class ModelInferenceStatusView(APIView):
    """模型推理状态查询视图"""

    permission_classes = [IsAuthenticated]

    def get(self, request, inference_log_id):
        """根据推理日志ID查询推理状态"""
        try:
            # 根据推理日志ID查询
            try:
                inference_log = ModelInferenceLog.objects.get(id=inference_log_id)
                model = inference_log.training_model
            except ModelInferenceLog.DoesNotExist:
                return Response({
                    'success': False,
                    'message': f'推理日志ID {inference_log_id} 不存在',
                    'status': 'not_found'
                }, status=status.HTTP_404_NOT_FOUND)

            # 获取推理结果数据
            result_data = inference_log.result_data or {}

            # 判断推理状态
            if result_data.get('success') is True:
                # 推理成功 - 兼容单张和批量推理

                # 检查是否为批量推理结果
                if 'batch_results' in result_data:
                    # 批量推理结果 - 直接使用保存的batch_results数据
                    batch_results = result_data.get('batch_results', [])

                    response_data = {
                        'success': True,
                        'message': '批量推理任务已完成',
                        'data': {
                            'inference_log_id': inference_log.id,
                            'model_id': model.id,
                            'model_name': model.model_name,
                            'result': {
                                'status': 'completed',
                                'message': f'批量推理任务已成功完成，处理了 {result_data.get("processed_images", len(batch_results))} / {result_data.get("total_images", len(batch_results))} 张图片',
                                'batch_results': batch_results,
                                'total_images': result_data.get('total_images', len(batch_results)),
                                'processed_images': result_data.get('processed_images', len(batch_results)),
                                'confidence_threshold': result_data.get('confidence_threshold', inference_log.confidence_threshold),
                                'iou_threshold': result_data.get('iou_threshold', 0.45),
                                'max_detections': result_data.get('max_detections', 1000),
                                'has_images': len(batch_results) > 0,
                                'image_format': 'jpeg',
                                'inference_log': result_data.get('inference_log', '')
                            }
                        },
                        'status': 'completed'
                    }
                else:
                    # 单张推理结果
                    response_data = {
                        'success': True,
                        'message': '推理任务已完成',
                        'data': {
                            'inference_log_id': inference_log.id,
                            'model_id': model.id,
                            'model_name': model.model_name,
                            'result': {
                                'status': 'completed',
                                'message': '推理任务已成功完成',
                                'detections': result_data.get('detections', []),
                                'detection_count': result_data.get('detection_count', 0),
                                'inference_time_ms': result_data.get('inference_time_ms', 0.0),
                                'confidence_threshold': result_data.get('confidence_threshold', inference_log.confidence_threshold),
                                'iou_threshold': result_data.get('iou_threshold', 0.45),
                                'max_detections': result_data.get('max_detections', 1000),
                                'result_path': result_data.get('result_path', ''),
                                'image_data': result_data.get('image_data'),
                                'image_format': result_data.get('image_format', 'jpeg'),
                                'has_image': result_data.get('has_image', False),
                                'image_message': result_data.get('image_message', '检测结果图片已生成' if result_data.get('has_image') else '无图片数据')
                            }
                        },
                        'status': 'completed'
                    }

            elif result_data.get('success') is False:
                # 推理失败
                error_message = result_data.get('error', '推理任务失败')
                response_data = {
                    'success': False,
                    'message': '推理任务失败',
                    'data': {
                        'inference_log_id': inference_log.id,
                        'model_id': model.id,
                        'model_name': model.model_name,
                        'result': {
                            'status': 'failed',
                            'message': error_message,
                            'detections': [],
                            'detection_count': 0,
                            'inference_time_ms': 0.0,
                            'confidence_threshold': result_data.get('confidence_threshold', inference_log.confidence_threshold),
                            'iou_threshold': result_data.get('iou_threshold', 0.45),
                            'max_detections': result_data.get('max_detections', 1000),
                            'result_path': '',
                            'image_data': None,
                            'image_format': None,
                            'has_image': False,
                            'image_message': '推理失败，无法生成结果图片'
                        }
                    },
                    'status': 'failed'
                }

            else:
                # 推理正在进行中
                response_data = {
                    'success': True,
                    'message': '推理任务正在处理中',
                    'data': {
                        'inference_log_id': inference_log.id,
                        'model_id': model.id,
                        'model_name': model.model_name,
                        'result': {
                            'status': 'processing',
                            'message': '推理任务正在后台处理中，请稍后查询结果',
                            'detections': [],
                            'detection_count': 0,
                            'inference_time_ms': 0.0,
                            'confidence_threshold': inference_log.confidence_threshold or 0.5,
                            'iou_threshold': 0.45,
                            'max_detections': 1000,
                            'result_path': '',
                            'image_data': None,
                            'image_format': None,
                            'has_image': False,
                            'image_message': '推理正在进行中，图片数据将在完成后提供'
                        }
                    },
                    'status': 'processing'
                }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"查询推理状态失败: {e}", exc_info=True)
            return Response({
                'success': False,
                'message': f'查询推理状态失败: {str(e)}',
                'status': 'error'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelConversionLogView(APIView):
    """模型转换日志视图"""

    permission_classes = [IsAuthenticated]
    
    def get(self, request, model_id=None):
        """获取模型转换日志"""
        try:
            if model_id:
                # 获取特定模型的转换日志
                model = get_object_or_404(TrainingModel, id=model_id)
                logs = ModelConversionLog.objects.filter(training_model=model)
            else:
                # 获取用户所有的转换日志
                user_models = TrainingModel.objects.filter(created_by=request.user)
                logs = ModelConversionLog.objects.filter(training_model__in=user_models)

            # 分页
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            paginator = Paginator(logs.order_by('-start_time'), page_size)
            page_obj = paginator.get_page(page)
            
            # 序列化数据
            serializer = ModelConversionLogSerializer(page_obj.object_list, many=True)
            
            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'num_pages': paginator.num_pages
                }
            })
        except Exception as e:
            logger.error(f"获取转换日志失败: {e}")
            return Response({
                'success': False,
                'message': f'获取转换日志失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ModelInferenceLogView(APIView):
    """模型推理日志视图"""

    permission_classes = [IsAuthenticated]

    def get(self, request, model_id=None):
        """获取模型推理日志"""
        try:
            if model_id:
                # 获取特定模型的推理日志
                model = get_object_or_404(
                    TrainingModel,
                    id=model_id,
                    created_by=request.user
                )
                logs = ModelInferenceLog.objects.filter(training_model=model)
            else:
                # 获取用户所有的推理日志
                user_models = TrainingModel.objects.filter(created_by=request.user)
                logs = ModelInferenceLog.objects.filter(training_model__in=user_models)

            # 分页
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 20))
            paginator = Paginator(logs.order_by('-created_time'), page_size)
            page_obj = paginator.get_page(page)

            # 序列化数据
            serializer = ModelInferenceLogSerializer(page_obj.object_list, many=True)

            return Response({
                'success': True,
                'data': serializer.data,
                'pagination': {
                    'total': paginator.count,
                    'page': page,
                    'page_size': page_size,
                    'total_pages': paginator.num_pages
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"获取推理日志失败: {e}")
            return Response({
                'success': False,
                'message': f'获取推理日志失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingModelViewSet(viewsets.ModelViewSet):
    """训练模型视图集"""
    queryset = TrainingModel.objects.all()
    serializer_class = TrainingModelSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        """根据操作选择序列化器"""
        if self.action == 'retrieve':
            return TrainingModelDetailSerializer
        return TrainingModelSerializer
    
    def get_queryset(self):
        """根据查询参数过滤模型列表"""
        queryset = TrainingModel.objects.all()
        
        # 按任务ID过滤
        task_id = self.request.query_params.get('task_id')
        if task_id:
            queryset = queryset.filter(task_id=task_id)
            
        # 按是否为最佳模型过滤
        is_best = self.request.query_params.get('is_best')
        if is_best:
            is_best_bool = is_best.lower() == 'true'
            queryset = queryset.filter(is_best=is_best_bool)
            
        # 按训练轮次过滤
        epoch = self.request.query_params.get('epoch')
        if epoch:
            queryset = queryset.filter(epoch=epoch)
            
        # 按是否已转换过滤
        # is_converted = self.request.query_params.get('is_converted')
        # if is_converted:
        #     is_converted_bool = is_converted.lower() == 'true'
        #     queryset = queryset.filter(is_converted=is_converted_bool)
            
        return queryset
    
    @action(detail=False, methods=['get'])
    def refresh_models(self, request):
        """
        从训练服务器刷新模型信息
        
        参数:
            task_id: 训练任务ID
        """
        task_id = request.query_params.get('task_id')
        if not task_id:
            return Response({"error": "必须提供任务ID"}, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            # 获取训练任务
            task = get_object_or_404(TrainingTask, id=task_id)
            
            # 查找与此任务关联的模型，以获取服务器信息
            server_ip = None
            server_port = None
            server_password = None
            
            # 首先检查任务本身是否有服务器信息
            if hasattr(task, 'server_ip') and task.server_ip:
                server_ip = task.server_ip
                server_port = task.server_port
                server_password = task.server_password
            else:
                # 查找关联的模型
                models = TrainingModel.objects.filter(task=task)
                for model in models:
                    if model.server_ip and model.server_port and model.server_password:
                        server_ip = model.server_ip
                        server_port = model.server_port
                        server_password = model.server_password
                        break
            
            if not server_ip or not server_port or not server_password:
                return Response({"error": "无法获取服务器连接信息"}, status=status.HTTP_404_NOT_FOUND)
            
            # 创建服务器连接信息
            server_info = {
                'ip': server_ip,
                'port': server_port,
                'password': server_password,
                'username': 'root'  # 默认用户名
            }
            
            # 创建ModelConverter实例
            converter = ModelConverter(server_info)
            
            # 获取模型信息
            # 注意：ModelConverter目前没有直接获取模型列表的方法
            # 这里需要添加一个新方法或使用现有方法的组合
            
            # 假设我们添加了一个get_models_info方法
            # models_info = converter.get_models_info(task.model_path)
            
            # 由于ModelConverter没有此方法，我们暂时返回已有的模型
            models = TrainingModel.objects.filter(task=task)
            serializer = self.get_serializer(models, many=True)
            
            # 关闭连接
            converter.disconnect()
            
            return Response({
                "message": "模型信息刷新成功",
                "models": serializer.data
            })
                
        except Exception as e:
            return Response({"error": f"刷新模型信息失败: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
