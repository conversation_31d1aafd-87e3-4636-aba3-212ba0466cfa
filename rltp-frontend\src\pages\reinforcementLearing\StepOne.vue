<template>
  <div class="scenario-container">
    <div class="left-panel">
      <div class="available-scenarios">
        <div class="header-row">
          <div class="title-section">
            <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="" />
            <div class="labelColor title">可用想定({{ scenarios.length }})</div>
          </div>
        </div>
        <div class="container">
          <div
            class="scenario-card"
            v-for="(item, index) in scenarios"
            :key="index"
            :class="{ selected: selectedScenarioIndex === index }"
            @click="selectScenario(index)"
          >
            <div class="corner-top-left"></div>
            <div class="corner-top-right" v-if="selectedScenarioIndex != index"></div>
            <div class="corner-bottom-right"></div>
            <div class="corner-bottom-left"></div>
            <div class="selected-icon" v-if="selectedScenarioIndex === index">
              <img src="../../assets/reinforcementImages/btn_xz.png" />
            </div>
            <div class="scenario-title">{{ item.scenario.title }}</div>
            <div class="scenario-items">
              <div class="basic-info">
                <span class="info-left"> {{ item.scenario.status }}</span>
                <span class="info-right"> {{ item.scenario.environmenttype }}</span>
              </div>
              <span class="info-text">{{ item.scenario.info }}</span>
              <div class="detail-info">
                <div class="top">
                  <div class="top-item">
                    <img src="../../assets/reinforcementImages/kp_icon_dz.png" />
                    <p :title="item.scenario.detailinfo.detaillocation">
                      {{ item.scenario.detailinfo.detaillocation }}
                    </p>
                  </div>
                  <div class="top-item">
                    <img src="../../assets/reinforcementImages/kp_icon_sj.png" />
                    <p :title="item.scenario.detailinfo.detailtime">
                      {{ item.scenario.detailinfo.detailtime }}
                    </p>
                  </div>
                </div>
                <div class="bottom">
                  <div class="bottom-item">
                    <img src="../../assets/reinforcementImages/kp_icon_wf.png" />
                    <p :title="item.scenario.detailinfo.detailour">
                      {{ item.scenario.detailinfo.detailour }}
                    </p>
                  </div>
                  <div class="bottom-item">
                    <img src="../../assets/reinforcementImages/kp_icon_df.png" />
                    <p :title="item.scenario.detailinfo.detailenemy">
                      {{ item.scenario.detailinfo.detailenemy }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="add-time">{{ item.scenario.addtime }}</div>
            </div>
          </div>
        </div>
        <div class="import-section" @click="importScenario">
          <div class="bts">
            <q-btn class="import-btn roundBox" color="primary" label="导入想定">
              <img
                src="../../assets/reinforcementImages/icon_drxd.png"
                class="btn-icon"
              />
            </q-btn>
          </div>
        </div>
      </div>
    </div>

    <div class="right-panel">
      <div class="scenario-detail">
        <div class="detail-top">
          <div class="detail-left">
            <div class="top-title">
              <div class="title-left">
                <div class="header-row">
                  <div class="title-section">
                    <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="" />
                    <div class="labelColor title">想定详情</div>
                  </div>
                </div>
                <div class="center-content">
                  <span class="one-content">{{ scenariodetail.type }}</span>
                  <span class="two-content">{{ scenariodetail.info }}</span>
                </div>
              </div>
              <div class="right-content">
                <span>{{ scenariodetail.status }}</span>
              </div>
            </div>
            <div class="bottom-content">
              <div
                v-for="(item, index) in scenariodetail.detailinfo"
                :key="index"
                class="info-item"
              >
                <img :src="getImageByType(item.type)" />
                <div class="info-text">
                  <div class="row q-col-gutter-md items-center">
                    <q-select
                      v-model="item.info"
                      :options="getOptionsByType(item.type)"
                      emit-value
                      map-options
                      class="info-main"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="detail-right">
            <img src="../../assets/images/ai-pilot.png" class="detail-image" />
          </div>
        </div>

        <div class="detail-bottom">
          <div class="stats-section">
            <h5>场景统计</h5>
            <div class="stats-info">
              <div class="info-item">
                <span>我方总兵力:</span>
                <p>{{ detailedInfo.Ourforces }}</p>
              </div>
              <div class="info-item">
                <span>敌方总兵力:</span>
                <p>{{ detailedInfo.Enemyforces }}</p>
              </div>
              <div class="info-item">
                <span>兵力比:</span>
                <p>{{ detailedInfo.TroopRatio }}</p>
              </div>
            </div>
          </div>
          <div class="create-info">
            <h5>创建信息</h5>
            <div class="stats-info">
              <div class="info-item">
                <span>创建时间:</span>
                <p>{{ detailedInfo.AddTime }}</p>
              </div>
              <div class="info-item">
                <span>场景ID:</span>
                <p>{{ detailedInfo.SceneID }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bottom-sections">
        <div class="section force-comparison">
          <div class="header-row">
            <div class="title-section">
              <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="" />
              <div class="labelColor title">兵力对比</div>
            </div>
          </div>
          <div class="force-container">
            <div
              v-for="(force, index) in forceComparison"
              :key="index"
              class="force-info"
            >
              <div class="onetop">
                <img :src="getIconByType(force.type)" />
                <h5
                  :style="{
                    color:
                      force.type === 1 ? 'rgba(40, 124, 208, 1)' : 'rgba(232, 38, 38, 1)',
                  }"
                >
                  {{ getTitleByType(force.type) }}
                </h5>
              </div>
              <!-- 类型0: 红方兵力 -->
              <div v-if="force.type === 0" class="force-detail">
                <!-- 第一行：标题和数量输入框 -->
                <div class="fleet-count-control">
                  <span>{{ force.name }}</span>
                  <div class="count-input">
                    <span class="count-title">数量</span>
                    <div class="row items-center customAdd count-btn">
                      <q-btn
                        flat
                        dense
                        icon="remove"
                        size="sm"
                        @click="decreaseCount(force, index)"
                      />
                      <div class="q-mx-md">{{ force.totalSum }}</div>
                      <q-btn
                        flat
                        dense
                        icon="add"
                        size="sm"
                        @click="increaseCount(force, index)"
                      />
                    </div>
                  </div>
                </div>

                <!-- 小卡片滑动区域 -->
                <div
                  class="card-slider"
                  @mousedown="startDrag"
                  @mousemove="drag"
                  @mouseup="endDrag"
                  @mouseleave="endDrag"
                  @touchstart="startDrag"
                  @touchmove.prevent="drag"
                  @touchend="endDrag"
                >
                  <div class="slider-container">
                    <div
                      v-for="(fleet, fleetIndex) in force.fleets"
                      :key="fleetIndex"
                      class="fleet-card"
                      :style="{ transform: `translateX(-${currentSlide * 100}%)` }"
                    >
                      <div class="card-header">
                        <span class="card-title">{{ fleet.name }}</span>
                        <img
                          src="../../assets/reinforcementImages/icon_save.png"
                          @click="saveCardData(force, fleet)"
                        />
                      </div>
                      <div class="card-info-grid">
                        <div
                          v-for="(item, itemIndex) in fleet.type"
                          :key="itemIndex"
                          class="equipment-item"
                        >
                          <span class="equipment-desc" :title="item.name">
                            {{
                              item.name.length > 4
                                ? item.name.slice(0, 4) + "..."
                                : item.name
                            }}
                          </span>
                          <div class="count-input">
                            <div class="row items-center customAdd">
                              <q-btn
                                flat
                                dense
                                icon="remove"
                                size="sm"
                                @click="
                                  decreaseItemCount(force, index, fleetIndex, itemIndex)
                                "
                              />
                              <div class="q-mx-md">{{ item.count }}</div>
                              <q-btn
                                flat
                                dense
                                icon="add"
                                size="sm"
                                @click="
                                  increaseItemCount(force, index, fleetIndex, itemIndex)
                                "
                              />
                            </div>
                          </div>
                        </div>
                        <div class="card-status">
                          <span class="status-desc">状态：</span>
                          <img
                            v-if="fleet.status === true"
                            src="../../assets/reinforcementImages/btn_1.png"
                            @click="toggleFleetStatus(fleet)"
                          />
                          <img
                            v-else
                            src="../../assets/reinforcementImages/btn_2.png"
                            @click="toggleFleetStatus(fleet)"
                          />
                        </div>
                      </div>

                      <!-- 滑动圆圈指示器 -->
                      <div class="slider-indicator">
                        <span
                          v-for="(fleet, idx) in force.fleets"
                          :key="idx"
                          :class="{ active: currentSlide === idx }"
                          @click="currentSlide = idx"
                        ></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 类型蓝方兵力 -->
              <div v-else class="force-detail">
                <div class="fleet-count-control">
                  <span>{{ force.name }}</span>
                  <img
                    src="../../assets/reinforcementImages/icon_save.png"
                    @click="saveEnemyFleetData(force)"
                  />
                </div>
                <div class="fleet-item">
                  <div class="card-info-grid">
                    <div class="count-input">
                      <span class="count-title">数量</span>
                      <div class="row items-center customAdd">
                        <q-btn
                          flat
                          dense
                          icon="remove"
                          size="sm"
                          @click="decreaseCount(force, index)"
                        />
                        <div class="q-mx-md">{{ force.totalSum }}</div>
                        <q-btn
                          flat
                          dense
                          icon="add"
                          size="sm"
                          @click="increaseCount(force, index)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="fleet-force">
                  <div
                    v-for="(fleet, fleetIndex) in force.fleets"
                    :key="fleetIndex"
                    class="fleet-item"
                  >
                    <div class="card-info-grid">
                      <div class="count-input">
                        <span class="card-title">{{ fleet.name }}</span>
                        <div class="row items-center customAdd">
                          <q-btn
                            flat
                            dense
                            icon="remove"
                            size="sm"
                            @click="decreaseFleetCount(force, index, fleetIndex)"
                          />
                          <div class="q-mx-md">{{ fleet.count }}</div>
                          <q-btn
                            flat
                            dense
                            icon="add"
                            size="sm"
                            @click="increaseFleetCount(force, index, fleetIndex)"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="section targetmask">
          <div class="header-row">
            <div class="title-section">
              <img class="samllIcon" src="../../assets/images/icon_dz.png" alt="" />
              <div class="labelColor title">目标任务</div>
            </div>
            <div class="right">
              <img
                src="../../assets/reinforcementImages/icon_save.png"
                @click="saveMissionObjectives()"
              />
            </div>
          </div>
          <div class="task-step">
            <el-steps direction="vertical" align-center class="steps">
              <el-step
                v-for="(mission, index) in missionObjectives"
                :key="index"
                finish-status="success"
              >
                <template #description>
                  <q-input
                    v-model.trim="mission.title"
                    debounce="500"
                    class="steps-input"
                    borderless
                    dense
                    type="textarea"
                  >
                  </q-input>
                </template>
              </el-step>
            </el-steps>
          </div>
        </div>
      </div>
    </div>
    <!-- 下一步按钮 -->
    <div class="next">
      <q-btn class="nextBtn roundBox" color="primary" label="下一步" @click="nextStep" />
    </div>
  </div>
  <!-- 提示弹窗 -->
  <q-dialog v-model="showTipDialog" persistent>
    <div class="tip-dialog">
      <div class="tip-header">提醒</div>
      <div class="tip-content" v-if="isAddOperation">
        当前数量总和已达到上限，无法继续增加！
      </div>
      <div class="tip-content" v-else>
        当前总数量减去 1 后将小于下面每一个目标加和，不能进行减操作！
      </div>
      <div class="tip-actions">
        <q-btn class="tip-confirm-btn" @click="closeTipDialog">确定</q-btn>
      </div>
    </div>
  </q-dialog>
  <!-- 导入弹窗 -->
  <q-dialog v-model="showImportDialog" persistent>
    <div class="tip-dialog">
      <div class="imprt-container">
        <div class="q-gutter-sm row items-center justify-center">
          <q-uploader
            url="http://localhost:4444/upload"
            label="导入想定"
            :multiple="false"
            accept=".json"
            @uploaded="handleUploaded"
            @add="handleFileAdd"
          />
        </div>
      </div>
      <div class="tip-actions">
        <q-btn class="tip-cancel-btn" @click="showImportDialog = false">取消</q-btn>
        <q-btn class="tip-confirm-btn" @click="showImportDialog = false">确定</q-btn>
      </div>
    </div>
  </q-dialog>
</template>

<script setup>
import { ref, defineEmits, watch, onMounted } from "vue";
import { useReinforcementLearningStore } from "../../stores/reinforcementLearingStore";
import { saveStepData } from "../../request/workFlow/index.js";
import { usePlugin } from "composables/plugin.js";
const { notify } = usePlugin();
import { useRoute } from "vue-router";
const route = useRoute();
const task_id = route.query.task_id;
const emit = defineEmits(["next-step", "prev-step"]);

const selectedScenarioIndex = ref(0);

// 更新 selectScenario 函数，使其能够正确处理场景切换
const selectScenario = (index) => {
  selectedScenarioIndex.value = index;
  // 加载选中场景的详细数据
  const selectedScenario = scenarios.value[index];
  if (selectedScenario) {
    processScenarioData(selectedScenario);
  }
};
const reinforcementLearingStore = useReinforcementLearningStore();

const scenarios = ref([]);
const scenariodetail = ref({});
function getOptionsByType(type) {
  const optionsMap = {
    Environment: [
      { label: "空中环境", value: "空中环境" },
      { label: "城市环境", value: "城市环境" },
    ],
    Terrain: [
      { label: "高空地形", value: "高空地形" },
      { label: "平原地形", value: "平原地形" },
      { label: "山地地形", value: "山地地形" },
    ],
    Weather: [
      { label: "晴天", value: "晴天" },
      { label: "多云", value: "多云" },
      { label: "雨天", value: "雨天" },
      { label: "阴天", value: "阴天" },
    ],
  };
  return optionsMap[type] || [];
}
const forceComparison = ref([]);

const missionObjectives = ref([]);
//导入想定内容
const showImportDialog = ref(false);
const importScenario = () => {
  showImportDialog.value = true;
};
// 处理文件添加事件，阻止自动上传
function handleFileAdd(files) {
  // 只处理JSON文件
  const jsonFile = files.filter((file) => file.name.endsWith(".json"))[0];
  if (jsonFile) {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target.result);
        processImportedData(jsonData);
        showImportDialog.value = false;
        notify("想定导入成功", "positive");
      } catch (error) {
        console.error("JSON解析错误:", error);
        notify("文件格式错误，请确保是有效的JSON文件", "negative");
      }
    };
    reader.readAsText(jsonFile);
  } else {
    notify("请选择JSON格式的文件", "warning");
  }
  return false;
}
// 处理上传完成事件
function handleUploaded() {}
function processImportedData(jsonData) {
  if (
    jsonData.scenarios &&
    Array.isArray(jsonData.scenarios) &&
    jsonData.scenarios.length > 0
  ) {
    // 将导入的场景添加到scenarios数组中
    scenarios.value = [...jsonData.scenarios];

    // 默认选中第一个场景
    selectedScenarioIndex.value = 0;

    // 获取第一个场景的详细数据
    const firstScenario = jsonData.scenarios[0];

    // 更新各个ref数据
    if (
      firstScenario.scenariodetail &&
      typeof firstScenario.scenariodetail === "object"
    ) {
      scenariodetail.value = {
        ...firstScenario.scenariodetail,
        detailinfo: Array.isArray(firstScenario.scenariodetail.detailinfo)
          ? firstScenario.scenariodetail.detailinfo
          : [],
      };
    }

    if (firstScenario.forceComparison && Array.isArray(firstScenario.forceComparison)) {
      forceComparison.value = [...firstScenario.forceComparison];
    }

    if (
      firstScenario.missionObjectives &&
      Array.isArray(firstScenario.missionObjectives)
    ) {
      missionObjectives.value = [...firstScenario.missionObjectives];
    }

    if (firstScenario.detailedInfo && typeof firstScenario.detailedInfo === "object") {
      detailedInfo.value = { ...firstScenario.detailedInfo };
    }
  } else {
    // 原有的根级别数据处理逻辑
    if (jsonData.scenariodetail && typeof jsonData.scenariodetail === "object") {
      scenariodetail.value = {
        ...jsonData.scenariodetail,
        detailinfo: Array.isArray(jsonData.scenariodetail.detailinfo)
          ? jsonData.scenariodetail.detailinfo
          : [],
      };
    }

    if (jsonData.forceComparison && Array.isArray(jsonData.forceComparison)) {
      forceComparison.value = [...jsonData.forceComparison];
    }

    if (jsonData.missionObjectives && Array.isArray(jsonData.missionObjectives)) {
      missionObjectives.value = [...jsonData.missionObjectives];
    }

    if (jsonData.detailedInfo && typeof jsonData.detailedInfo === "object") {
      detailedInfo.value = { ...jsonData.detailedInfo };
    }
  }
}

// 计算我方总兵力
const calculateOurForces = () => {
  const ourForces = forceComparison.value.find((force) => force.type === 0);
  return ourForces ? ourForces.totalSum : 0;
};

// 计算兵力比
function gcd(a, b) {
  return b === 0 ? a : gcd(b, a % b);
}
const calculateEnemyForces = () => {
  const enemyForces = forceComparison.value.find((force) => force.type === 1);
  return enemyForces ? enemyForces.totalSum : 0;
};
const calculateTroopRatio = () => {
  const ourForces = calculateOurForces();
  const enemyForces = calculateEnemyForces();

  if (enemyForces === 0) {
    return "0:1";
  }

  const divisor = gcd(ourForces, enemyForces);
  return `${Math.floor(ourForces / divisor)}:${Math.floor(enemyForces / divisor)}`;
};

// 监听兵力变化，更新统计信息
watch(
  forceComparison,
  () => {
    detailedInfo.value.Ourforces = calculateOurForces().toString();
    detailedInfo.value.Enemyforces = calculateEnemyForces().toString();
    detailedInfo.value.TroopRatio = calculateTroopRatio();
  },
  { deep: true }
);

const detailedInfo = ref({
  Ourforces: calculateOurForces().toString(),
  Enemyforces: calculateEnemyForces().toString(),
  TroopRatio: calculateTroopRatio(),
  AddTime: "2025/01/20",
  SceneID: "92.0%",
});

// 用于处理单个场景的数据
const processScenarioData = (scenarioData) => {
  if (scenarioData.scenariodetail && typeof scenarioData.scenariodetail === "object") {
    scenariodetail.value = {
      ...scenarioData.scenariodetail,
      detailinfo: Array.isArray(scenarioData.scenariodetail.detailinfo)
        ? scenarioData.scenariodetail.detailinfo
        : [],
    };
  }

  if (scenarioData.forceComparison && Array.isArray(scenarioData.forceComparison)) {
    forceComparison.value = [...scenarioData.forceComparison];
  }

  if (scenarioData.missionObjectives && Array.isArray(scenarioData.missionObjectives)) {
    missionObjectives.value = [...scenarioData.missionObjectives];
  }

  if (scenarioData.detailedInfo && typeof scenarioData.detailedInfo === "object") {
    detailedInfo.value = { ...scenarioData.detailedInfo };
  }
};
onMounted(() => {
  if (task_id) {
    reinforcementLearingStore.setCurrentTaskId(task_id);
  }
  if (reinforcementLearingStore.stepOneData) {
    const storeData = reinforcementLearingStore.stepOneData;
    if (
      storeData.scenarios &&
      Array.isArray(storeData.scenarios) &&
      storeData.scenarios.length > 0
    ) {
      scenarios.value = storeData.scenarios;
      selectedScenarioIndex.value = 0;
      const firstScenario = storeData.scenarios[0];
      if (firstScenario) {
        processScenarioData(firstScenario);
      }
    } else {
      // 初始化默认数据结构
      scenarios.value = [];
      scenariodetail.value = {
        status: "",
        type: "",
        info: "",
        detailinfo: [],
      };
      forceComparison.value = [];
      missionObjectives.value = [];
      detailedInfo.value = {
        Ourforces: "",
        Enemyforces: "",
        TroopRatio: "",
        AddTime: new Date().toISOString().split("T")[0].replace(/-/g, "/"),
        SceneID: "",
      };
    }
  }
});
// 下一步按钮点击事件
async function nextStep() {
  const taskId = task_id || reinforcementLearingStore.currentTaskId;
  if (!taskId) {
    // notify("任务ID缺失，请先选择任务", "warning");
    return;
  }
  try {
    // 创建一个新的scenarios数组，保留所有场景的数据
    const updatedScenarios = scenarios.value.map((scenario, index) => {
      // 为当前选中的场景更新详细数据
      if (index === selectedScenarioIndex.value) {
        return {
          scenario: scenario.scenario, // 保留原始场景数据
          scenariodetail: scenariodetail.value,
          forceComparison: forceComparison.value,
          missionObjectives: missionObjectives.value,
          detailedInfo: detailedInfo.value,
        };
      }
      // 对于未选中的场景，保留已有数据
      return scenario;
    });

    const stepData = {
      scenarios: updatedScenarios,
    };

    // 保存数据到API
    const response = await saveStepData({
      task_id: taskId,
      step_number: 1, // 当前是第一步
      step_data: stepData,
    });

    if (response.success) {
      // 更新store中的数据
      reinforcementLearingStore.updateStepOneData(stepData);

      // notify("第一步数据保存成功", "positive");
      emit("next-step");
    } else {
      // notify("保存失败: " + (response.message || "未知错误"), "negative");
      console.log("保存失败", response.message);
    }
  } catch (error) {
    console.error("保存步骤数据失败:", error);

    // 根据不同错误类型提供更具体的错误信息
    let errorMessage = "保存失败";
    if (error.response && error.response.data && error.response.data.message) {
      errorMessage += ": " + error.response.data.message;
    } else if (error.message) {
      errorMessage += ": " + error.message;
    } else {
      errorMessage += ": 网络错误或服务器异常";
    }

    // notify(errorMessage, "negative");
  }
}

function getImageByType(type) {
  switch (type) {
    case "Environment":
      return "../../src/assets/reinforcementImages/xq_icon_hj.png";
    case "Terrain":
      return "../../src/assets/reinforcementImages/xq_icon_dx.png";
    case "Weather":
      return "../../src/assets/reinforcementImages/xq_icon_tq.png";
    case "持续时间":
      return "../../src/assets/reinforcementImages/xq_icon_cxsj.png";
    default:
      return "";
  }
}

function getIconByType(type) {
  if (type === 0) {
    return "../../src/assets/reinforcementImages/icon_wfbl.png";
  } else if (type === 1) {
    return "../../src/assets/reinforcementImages/icon_dfbl.png";
  }
  return "";
}

function getTitleByType(type) {
  if (type === 0) {
    return "红方";
  } else if (type === 1) {
    return "蓝方";
  }
  return "";
}
const currentSlide = ref(0);
const isDragging = ref(false);
const startX = ref(0);
const currentX = ref(0);
const dragOffset = ref(0);
function startDrag(e) {
  isDragging.value = true;
  startX.value = e.type === "touchstart" ? e.touches[0].clientX : e.clientX;
  currentX.value = -currentSlide.value * 100;
}
function drag(e) {
  if (!isDragging.value) return;
  const x = e.type === "touchmove" ? e.touches[0].clientX : e.clientX;
  dragOffset.value = x - startX.value;
}
function endDrag() {
  if (!isDragging.value) return;
  isDragging.value = false;
  // 若拖动距离超过 50px 则切换卡片
  if (dragOffset.value > 50 && currentSlide.value > 0) {
    currentSlide.value--;
  } else if (
    dragOffset.value < -50 &&
    currentSlide.value < forceComparison.value[0].fleets.length - 1
  ) {
    currentSlide.value++;
  }
  dragOffset.value = 0;
}

// 减少数量函数
function decreaseCount(force, index) {
  if (force.type === 0) {
    // 红方兵力逻辑
    if (force.fleets.length > 0 && force.totalSum > 1) {
      if (currentSlide.value === force.fleets.length - 1) {
        currentSlide.value = Math.max(0, force.fleets.length - 2);
      }
      force.fleets.pop();
      force.totalSum--;
    }
  } else if (force.type === 1) {
    // 蓝方兵力逻辑
    const sumOfTargets = force.fleets.reduce((sum, fleet) => sum + fleet.count, 0);
    isAddOperation.value = false;
    if (sumOfTargets > force.totalSum - 1) {
      showTipDialog.value = true;
      return;
    }
    if (force.totalSum > 0) {
      force.totalSum--;
    }
  }
}

// 增加数量函数
const isAddOperation = ref(false);
function increaseCount(force, index) {
  if (force.type === 0) {
    // 红方兵力
    if (force.totalSum < 13) {
      const newFleet = JSON.parse(JSON.stringify(force.fleets[0]));
      newFleet.type.forEach((item) => {
        item.count = 0;
      });
      force.fleets.push(newFleet);
      force.totalSum++;
    }
  } else if (force.type === 1) {
    // 蓝方兵力
    if (force.totalSum < 16) {
      force.totalSum++;
    }
  }
}

// 增减单个舰队数量
function increaseFleetCount(force, index, fleetIndex) {
  const sum = force.fleets.reduce((acc, fleet) => acc + fleet.count, 0);
  if (sum < force.totalSum) {
    force.fleets[fleetIndex].count++;
  } else {
    isAddOperation.value = true;

    showTipDialog.value = true;
  }
}

function decreaseFleetCount(force, index, fleetIndex) {
  if (force.fleets[fleetIndex].count > 0) {
    force.fleets[fleetIndex].count--;
  }
}

// 增减单个装备数量
function increaseItemCount(force, index, fleetIndex, itemIndex) {
  const fleet = force.fleets[fleetIndex];
  const item = fleet.type[itemIndex];
  item.count++;
}

function decreaseItemCount(force, index, fleetIndex, itemIndex) {
  const fleet = force.fleets[fleetIndex];
  const item = fleet.type[itemIndex];
  if (item.count > 0) {
    item.count--;
  }
}

// 兵力对比状态切换
function toggleFleetStatus(fleet) {
  fleet.status = !fleet.status;
}
const showTipDialog = ref(false);
const closeTipDialog = () => {
  showTipDialog.value = false;
};
// 保存红方当前卡片数据
const saveCardData = (force, fleet) => {
  console.log("保存当前卡片数据:", force, fleet);
  // 可添加实际保存逻辑，如调用API或更新store
  // api.saveCardData({ force, fleet });
  // reinforcementLearingStore.updateCardData({ force, fleet });
};
//保存蓝方当前数据
const saveEnemyFleetData = (force) => {
  console.log("保存蓝方兵力数据:", force);
  // 后续可添加 API 调用等保存逻辑
  // api.post('/save-enemy-fleet', force);
};
// 保存任务目标
const saveMissionObjectives = () => {
  console.log("保存目标任务数据:", missionObjectives.value);
  // 后续可添加 API 调用等保存逻辑
  // api.post('/save-mission-objectives', missionObjectives.value);
};
</script>

<style lang="scss" scoped>
.scenario-container {
  display: flex;
  flex: 1;
  // min-height: 9.5rem;
  position: relative;
  .next {
    top: -4vh;
    right: 0;
    position: absolute;
    display: flex;
    justify-content: space-between;
    gap: 0.25rem;
    width: 10%;

    .prevBtn {
      margin-right: auto;
    }

    .nextBtn {
      margin-left: auto;
    }
  }
  .left-panel {
    min-height: 9.5rem;
    display: flex;
    flex-direction: column;
    // padding-right: 0.125rem;
    width: 23%;
    height: inherit;

    .available-scenarios {
      padding: 0.3375rem;
      flex-grow: 1;
      border: 0.025rem solid #707070;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      .container {
        height: 85%;
        overflow-y: auto;
        scrollbar-width: none;
        &::-webkit-scrollbar {
          display: none;
        }
        .scenario-card {
          position: relative;
          background: rgba(29, 45, 68, 0.4);
          border-radius: 0.0625rem;
          transition: border 0.3s, transform 0.3s, box-shadow 0.3s, background 0.3s;
          margin-bottom: 0.125rem;
          padding: 0.125rem;
          height: 3.65rem;
        }

        .scenario-card:hover {
          background: rgba(51, 102, 255, 0.18);
          transform: translateY(-0.0625rem);
          box-shadow: 0 0.05rem 0.15rem rgba(0, 0, 0, 0.15);
        }

        .scenario-card.selected {
          position: relative;
          background: rgba(51, 102, 255, 0.18);
          border-radius: 0.0625rem;
          margin-bottom: 0.125rem;
          padding: 0.125rem;
          .selected-icon {
            position: absolute;
            top: 0;
            right: 0;
          }
        }
        .scenario-card.selected:hover {
          background: rgba(51, 102, 255, 0.18);
          transform: translateY(-0.0625rem);
          box-shadow: 0 0.05rem 0.15rem rgba(0, 0, 0, 0.15);
        }
        .scenario-card .corner-top-left {
          position: absolute;
          top: 0;
          left: 0;
          width: 0.2rem;
          height: 0.2rem;
          border-top: 0.025rem solid rgba(112, 112, 112, 1);
          border-left: 0.025rem solid rgba(112, 112, 112, 1);
        }
        .scenario-card .corner-top-right {
          position: absolute;
          top: 0;
          right: 0;
          width: 0.2rem;
          height: 0.2rem;
          border-top: 0.025rem solid rgba(112, 112, 112, 1);
          border-right: 0.025rem solid rgba(112, 112, 112, 1);
        }
        .scenario-card .corner-bottom-right {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 0.2rem;
          height: 0.2rem;
          border-bottom: 0.025rem solid rgba(112, 112, 112, 1);
          border-right: 0.025rem solid rgba(112, 112, 112, 1);
        }

        .scenario-card .corner-bottom-left {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 0.2rem;
          height: 0.2rem;
          border-bottom: 0.025rem solid rgba(112, 112, 112, 1);
          border-left: 0.025rem solid rgba(112, 112, 112, 1);
        }
        .scenario-card.selected .corner-top-left {
          border-top: 0.025rem solid rgba(225, 225, 225, 1);
          border-left: 0.025rem solid rgba(225, 225, 225, 1);
        }

        .scenario-card.selected .corner-top-right {
          border-top: 0.025rem solid rgba(225, 225, 225, 1);
          border-right: 0.025rem solid rgba(225, 225, 225, 1);
        }

        .scenario-card.selected .corner-bottom-right {
          border-bottom: 0.025rem solid rgba(225, 225, 225, 1);
          border-right: 0.025rem solid rgba(225, 225, 225, 1);
        }

        .scenario-card.selected .corner-bottom-left {
          border-bottom: 0.025rem solid rgba(225, 225, 225, 1);
          border-left: 0.025rem solid rgba(225, 225, 225, 1);
        }
        .scenario-card {
          .scenario-title {
            margin: 0.125rem 0.0625rem 0.1875rem;
            height: 0.1875rem;
            line-height: 0.1875rem;
            color: #ffffff;
            font-size: 0.225rem;
          }

          .scenario-items {
            .basic-info {
              margin-bottom: 0.125rem;

              .info-left {
                border: 0.0125rem solid #c02c2c;
                border-radius: 0.2rem;
                width: 0.875rem;
                height: 0.4rem;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                color: #e82626;
                margin-right: 0.125rem;
                font-size: 0.2rem;
                background: rgba(192, 44, 44, 0.3);
              }

              .info-right {
                width: 1.25rem;
                height: 0.4rem;
                border: 0.0125rem solid;
                border-color: #ffffff;
                border-radius: 0.2rem;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                color: #ffffff;
                font-size: 0.2rem;
                background: rgba(225, 225, 225, 0.2);
              }
            }

            .info-text {
              display: block;
              flex: wrap;
              margin-bottom: 0.0625rem;
              color: #9cacc6;
              font-size: 0.2rem;
            }

            .detail-info {
              background-color: rgba(225, 225, 225, 0.1);
              margin-bottom: 0.1rem;
              border-radius: 0.1rem;
              padding: 0.25rem 0;
              .top,
              .bottom {
                display: flex;
                .top-item {
                  margin-bottom: 0.0625rem;
                }
                .top-item,
                .bottom-item {
                  width: 40%;
                  display: flex;
                  align-items: center;
                  margin-left: 0.35rem;
                  p {
                    color: #ffffff;
                    font-size: 0.2rem;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    margin-bottom: 0;
                  }
                }
              }
            }

            .add-time {
              color: #9cacc6;
              font-size: 0.1625rem;
            }
          }
        }
      }
      .import-section {
        margin-top: 0.125rem;
        .bts {
          display: flex;
          align-items: center;
          justify-content: center;
          .import-btn {
            width: 1.75rem;
            position: relative;
            padding: 0.15rem;
            .btn-icon {
              position: absolute;
              margin-left: 0.3rem;
              left: 0;
            }
            :deep(.q-btn__content) {
              margin-left: 0.3rem;
            }
          }
        }
      }
    }
  }
  .left-panel {
    margin-left: 0.125rem;
    position: relative;

    &::before {
      position: absolute;
      content: "";
      left: -0.05rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }
  .right-panel {
    display: flex;
    flex-direction: column;
    margin-left: 0.125rem;
    width: 77%;
    height: inherit;
    .scenario-detail {
      border: 0.025rem solid #707070;
      margin-bottom: 0.25rem;
      height: 45%;
      padding: 0.3375rem;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      .detail-top {
        display: flex;
        height: 70%;
        .detail-left {
          flex: 1;
        }

        .top-title {
          display: flex;
          justify-content: space-between;
          height: 70%;
          .title-left {
            .center-content {
              display: flex;
              flex-direction: column;
              margin-left: 0.375rem;

              .one-content {
                color: #ffffff;
                font-size: 0.225rem;
                margin-bottom: 0.1rem;
              }

              .two-content {
                color: #cea345;
                font-size: 0.225rem;
              }
            }
          }

          .right-content {
            margin: 0.75rem 0.75rem 0.45rem 0.75rem;
            width: 1.25rem;
            height: 0.55rem;
            border: 0.0125rem solid;
            border-color: #c02c2c;
            border-radius: 0.275rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(192, 44, 44, 0.3);

            span {
              font-size: 0.225rem;
              height: 0.55rem;
              line-height: 0.55rem;
              text-align: center;
              color: #c02c2c;
            }
          }
        }

        .bottom-content {
          display: flex;
          margin-left: 0.25rem;
          gap: 1.25rem;
          height: 20%;
          .info-item {
            display: flex;
            align-items: center;
            flex: 1;
            img {
              margin-right: 0.125rem;
            }

            .info-text {
              text-align: center;
              position: relative;
              .info-main {
                width: 2rem;
                color: #ffffff;
                font-size: 0.25rem;
                :deep(.q-field__inner) {
                  background: rgba(112, 112, 112, 0.2);
                  font-size: 0.225rem;
                }
                :deep(.q-field__native span) {
                  margin-left: 0.125rem;
                }
              }
            }
          }
          .info-item {
            position: relative;
            &:not(:last-child)::before {
              position: absolute;
              content: "";
              top: 0;
              right: 0.525rem;
              width: 0.0225rem;
              height: 100%;
              background: rgba(112, 112, 112, 1);
            }
          }
        }
        .detail-right {
          // margin: 0.175rem 0 0 0.375rem;
          margin: auto;
          width: 4.375rem;
          height: 2.15rem;

          .detail-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .detail-right {
          margin-left: 0.125rem;
          position: relative;

          &::before {
            position: absolute;
            content: "";
            left: -0.25rem;
            top: 0;
            width: 0.0225rem;
            height: 100%;
            background: rgba(112, 112, 112, 1);
          }
        }
      }

      .detail-bottom {
        margin-top: 0.2rem;
        padding-top: 0.3rem;
        display: flex;
        align-items: center;
        height: 30%;
        h5 {
          height: 0.325rem;
          line-height: 0.325rem;
          color: #cea345;
          font-size: 0.225rem;
          margin-right: 0.375rem;
        }
        .stats-info {
          display: flex;
          height: 0.45rem;
          gap: 0.8rem;
          .info-item {
            display: flex;
            height: 0.325rem;
            line-height: 0.4375rem;
            span {
              color: #ffffff;
              font-size: 0.225rem;
            }
            p {
              color: rgba(74, 180, 255, 1);
              font-size: 0.225rem;
            }
          }
        }
        .stats-section {
          position: relative;
          display: flex;
          align-items: center;
          padding-right: 0.625rem;
          height: 0.375rem;
          width: 50%;
          &::before {
            position: absolute;
            content: "";
            right: -0.1rem;
            top: 0;
            width: 0.0125rem;
            height: 100%;
            background: rgba(112, 112, 112, 1);
          }
        }
        .create-info {
          display: flex;
          align-items: center;
          padding-left: 0.625rem;
          height: 0.375rem;
          width: 50%;
        }
      }
      .detail-bottom {
        position: relative;
        &::after {
          position: absolute;
          content: "";
          top: 0;
          height: 0.0125rem;
          width: 100%;
          background: rgba(112, 112, 112, 1);
        }
      }
    }
    .bottom-sections {
      display: flex;
      height: 55%;
      gap: 0.25rem;
      background-color: #181a24;
      background-image: repeating-linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.05) 0,
        rgba(255, 255, 255, 0.01) 0.05rem,
        transparent 0.0125rem,
        transparent 0.1875rem
      );
      .targetmask {
        width: 25%;
        .header-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.125rem;

          .title-section {
            display: flex;
            align-items: center;

            .samllIcon {
              width: 0.2rem;
              height: 0.2rem;
              margin-right: 0.1875rem;
            }

            .title {
              color: #4ab4ff;
              font-size: 0.225rem;
            }
          }
          .right {
            img {
              width: 0.325rem;
              height: 0.325rem;
              cursor: pointer;
            }
          }
        }
      }
      .section {
        border: 0.025rem solid #707070;
        // padding: 0 0.1875rem;
        padding: 0.3375rem;
        display: flex;
        flex-direction: column;
        flex: 1;
        width: 75%;
        .task-step {
          .steps {
            margin-left: 0.125rem;
            margin-bottom: 0.5rem;

            :deep(.el-step__icon) {
              background: rgba(51, 106, 150, 1);
              width: 0.45rem;
              height: 0.45rem;
              margin-left: -0.1rem;
            }
            :deep(.el-step__icon-inner) {
              color: rgba(74, 180, 255, 1);
              font-size: 0.225rem;
            }
            :deep(.q-field__control-container) {
              height: 0.625rem;
            }
            .steps-input {
              margin-left: 0.25rem;
              width: 2.175rem;
            }
          }
        }
      }

      .force-comparison {
        display: flex;
        flex-direction: column;
        .count-input {
          display: flex;
          align-items: center;
          margin-top: 0.125rem;
          span {
            height: 0.325rem;
            line-height: 0.4375rem;
            color: #ffffff;
            font-size: 0.225rem;
            margin-right: 0.2rem;
          }
        }
        .fleet-count-control {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 25%;
          img {
            width: 0.325rem;
            height: 0.325rem;
            cursor: pointer;
          }
          span {
            height: 0.3625rem;
            line-height: 0.4813rem;
            color: #cea345;
            font-size: 0.225rem;
          }
          .count-title {
            height: 0.325rem;
            line-height: 0.4375rem;
            color: #ffffff;
            font-size: 0.225rem;
          }
        }
        .force-container {
          flex: 1;
          display: flex;
          gap: 0.25rem;

          .force-info {
            border: 0.0125rem solid rgba(0, 252, 255, 1);
            height: 95%;
            padding: 0 0.125rem;
            display: flex;
            flex-direction: column;
            flex: 1;
            border-radius: 0.125rem;
            background: rgba(0, 252, 255, 0.1);

            .onetop {
              border-bottom: 0.0125rem solid rgba(0, 252, 255, 1);
              height: 15%;
              img {
                width: 0.2476rem;
                height: 0.3011rem;
                margin: 0;
              }
              h5 {
                height: 0.3625rem;
                line-height: 0.3625rem;
                font-size: 0.225rem;
                margin: 0.125rem;
              }
            }
            .force-detail {
              height: 70%;
            }
            .fleet-force {
              height: 1.75rem;
              overflow-x: hidden;
              overflow-y: auto;
              .card-title {
                font-size: 0.2rem;
              }
            }
            .fleet-force::-webkit-scrollbar {
              width: 0.07rem;
            }

            .fleet-force::-webkit-scrollbar-track {
              background: rgba(0, 0, 0, 0.5);
              border-radius: 0.05rem;
              margin: 0.05rem 0;
            }

            .fleet-force::-webkit-scrollbar-thumb {
              background: #888;
              width: 0.05rem;
              border-radius: 0.05rem;
            }

            .fleet-force::-webkit-scrollbar-thumb:hover {
              background: #555;
            }
            .fleet-item {
              display: flex;
              flex-direction: column;
              margin-bottom: 0.0625rem;
              width: 4rem;
              .card-info-grid {
                margin-bottom: 0.1rem;
                height: 70%;
                .equipment-item {
                  display: flex;
                  align-items: center;
                  .equipment-desc {
                    height: 0.325rem;
                    line-height: 0.4375rem;
                    color: #ffffff;
                    font-size: 0.175rem;
                    margin-right: 0.25rem;
                  }
                }
              }
            }
          }
        }
        .card-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 15%;
          span {
            height: 0.3625rem;
            line-height: 0.4813rem;
            color: #4ab4ff;
            font-size: 0.225rem;
          }
          img {
            width: 0.325rem;
            height: 0.325rem;
            cursor: pointer;
          }
        }
        .card-slider {
          position: relative;
          width: 7.5rem;
          overflow: hidden;
          margin-top: 0.125rem;
          height: 90%;
          .slider-container {
            display: flex;
            transition: transform 0.3s ease;
            height: 100%;
            .fleet-card {
              background: rgba(4, 10, 31, 0.3);
              border-radius: 0.0625rem;
              padding: 0.1rem;
              margin-bottom: 0.1rem;
              flex: 0 0 100%;
              .card-info-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 0.1rem;
                margin-bottom: 0.1rem;
                .equipment-item {
                  display: flex;
                  align-items: center;
                  .equipment-desc {
                    width: 1rem;
                    height: 0.325rem;
                    line-height: 0.4375rem;
                    color: #ffffff;
                    font-size: 0.2rem;
                    margin-right: 0.15rem;
                  }
                }
              }
              .card-status {
                display: flex;
                align-items: center;
                img {
                  margin-left: 0.125rem;
                  cursor: pointer;
                }
                .status-desc {
                  width: 1rem;
                  height: 0.325rem;
                  color: #ffffff;
                  font-size: 0.2rem;
                }
              }
            }
          }
          .slider-indicator {
            display: flex;
            justify-content: center;
            gap: 0.05rem;
            margin-top: 0.4rem;
            height: 15%;
            span {
              width: 0.1rem;
              height: 0.1rem;
              border-radius: 50%;
              background-color: #ccc;
              cursor: pointer;
            }
            span.active {
              background-color: #333;
            }
          }
        }
      }
    }
  }
  .right-panel {
    position: relative;

    &::before {
      position: absolute;
      content: "";
      left: -0.05rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198);
      border-radius: 0.125rem;
    }

    &::after {
      position: absolute;
      content: "";
      left: -0.075rem;
      top: 0;
      width: 0.025rem;
      height: 100%;
      background: rgba(156, 172, 198, 0.5);
    }
  }
}
.onetop {
  display: flex;
  align-items: center;
  h2 {
    height: 0.1875rem;
    line-height: 0.1875rem;
    color: #ffffff;
    font-size: 0.25rem;
  }
  img {
    width: 0.2rem;
    height: 0.2rem;
    margin-right: 0.1875rem;
  }
}
.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.125rem;

  .title-section {
    display: flex;
    align-items: center;

    .samllIcon {
      width: 0.2rem;
      height: 0.2rem;
      margin-right: 0.1875rem;
    }

    .title {
      color: #4ab4ff;
      font-size: 0.225rem;
    }
  }

  .param-info {
    display: flex;
    align-items: center;
    gap: 0.25rem;

    .info-text {
      color: white;
      font-size: 0.2rem;
    }

    .tech-doc {
      display: flex;
      align-items: center;
      color: #63d4ff;
      font-size: 0.175rem;
      cursor: pointer;

      .doc-icon {
        width: 0.15rem;
        height: 0.15rem;
        margin-right: 0.0625rem;
      }
    }
  }
}
.tip-dialog {
  background: #102947;
  border: 0.05rem solid rgba(1, 74, 173, 0.8);
  border-radius: 0.125rem;
  padding: 0.25rem;
  min-width: 6rem;
  box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.3);
  .tip-header {
    color: #fff;
    font-size: 0.25rem;
    font-weight: bold;
    margin-bottom: 0.175rem;
    text-align: left;
  }

  .tip-content {
    color: #fff;
    font-size: 0.2rem;
    line-height: 1.5;
    margin-bottom: 0.3rem;
    text-align: left;
  }
  .imprt-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    :deep(.q-uploader__header) {
      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
    }
  }

  .tip-actions {
    display: flex;
    justify-content: space-between;
    .tip-confirm-btn {
      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
      color: white !important;
      border-radius: 0.0625rem;
      font-size: 0.175rem;
      min-width: 1.25rem;
      height: 0.5rem;

      &:hover {
        background: linear-gradient(135deg, #357abd 0%, #2a5a8a 100%) !important;
      }
    }

    .tip-cancel-btn {
      background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
      color: white !important;
      border-radius: 0.0625rem;
      font-size: 0.175rem;
      min-width: 1.25rem;
      height: 0.5rem;
      margin-left: 0.125rem;

      &:hover {
        background: linear-gradient(135deg, #357abd 0%, #2a5a8a 100%) !important;
      }
    }
  }
}
:deep(.q-btn__content) {
  font-size: 0.175rem;
}
</style>
