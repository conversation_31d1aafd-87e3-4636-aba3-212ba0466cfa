<!--
 * @Author: Szc
 * @Date: 2025-08-07 10:45:00
 * @LastEditors: Szc
 * @LastEditTime: 2025-08-28 17:26:29
 * @Description: 
-->
<template>
    <div class="content">
        <div class="top">
            <div class="left">
                <!-- 选择测试文件 -->
                <div class="section">
                    <div class="section-header">
                        <span class="section-title">选择测试文件：</span>
                    </div>

                    <div class="upload-section">
                        <div class="upload-list">
                            <!-- 已上传的图片 -->
                            <div v-for="(image, index) in uploadedImages" :key="index"
                                class="upload-item image-preview">
                                <img :src="image.url" class="preview-image" alt="预览">
                                <div class="delete-btn" @click="removeUploadedImage(index)">
                                    <img src="../../assets/images/icon_btn_delete.png" alt="删除" class="delete-icon">
                                </div>
                            </div>

                            <!-- 上传按钮 -->
                            <div class="upload-item">
                                <el-upload class="test-file-uploader" action="" :auto-upload="false"
                                    :show-file-list="false" :on-change="handleFileUpload"
                                    accept=".jpeg,.jpg,.png,.tiff,.tif,.bmp" multiple>
                                    <div class="upload-area">
                                        <img src="../../assets/images/icon_sc.png" alt="上传" class="upload-icon">
                                    </div>
                                </el-upload>
                            </div>
                        </div>

                        <div class="upload-tips">
                            可以上传测试图像（.jpeg/.jpg/.png/.tiff/.tif/.bmp 文件格式），文件体积不超过100MB，文件数量不超过8个
                        </div>
                    </div>
                </div>

                <!-- 模型信息显示 -->
                <div class="model-section">
                    <div class="model-info">
                        <span class="model-title">可用模型：</span>
                        <div v-if="modelOptions.length > 0" class="model-list">
                            <div v-for="model in modelOptions" :key="model.value" class="model-item">
                                <span class="model-name">{{ model.label }}</span>
                                <span class="model-status">已就绪</span>
                            </div>
                        </div>
                        <div v-else class="no-models">
                            <span class="no-models-text">{{ getTrainingTaskId() ? '暂无已转换的模型' : '请先完成训练并转换模型' }}</span>
                        </div>
                    </div>
                </div>



                <!-- 按钮区域 -->
                <div class="button-section">
                    <q-btn class="action-btn reset-btn" flat no-caps @click="resetAll">
                        重置
                    </q-btn>
                    <q-btn class="action-btn run-btn" flat no-caps :loading="inferenceLoading"
                        :disable="inferenceLoading || !canRunInference" @click="runTest">
                        {{ inferenceLoading ? '多模型推理中...' : '运行多模型推理' }}
                    </q-btn>
                    <q-btn class="action-btn save-btn" flat no-caps @click="saveTest">
                        保存
                    </q-btn>
                </div>
            </div>

            <div class="right">
                <!-- 运行结果 -->
                <div class="result-section">
                    <div class="result-header">
                        <img class="arrow-icon" src="../../assets/images/icon_dz.png" alt="">
                        <span class="result-title">运行结果</span>
                    </div>

                    <div class="result-content">
                        <!-- 空状态 -->
                        <div v-if="!showResult && !inferenceLoading" class="empty-result">
                            <div class="empty-text">请上传测试文件并点击运行按钮</div>
                        </div>

                        <!-- 推理加载状态 -->
                        <div v-if="inferenceLoading" class="loading-result">
                            <div class="loading-spinner">
                                <q-spinner-dots color="primary" size="3rem" />
                            </div>
                            <div class="loading-text">模型推理中，请稍候...</div>
                            <div class="loading-tips">推理任务正在后台处理中，请耐心等待</div>
                        </div>

                        <!-- 多模型推理结果 -->
                        <div v-if="showResult && !inferenceLoading && multiModelResults && multiModelResults.inference_type === 'multi_model'"
                            class="multi-model-result-display">

                            <!-- 整体统计信息 -->
                            <!-- <div class="overall-summary">
                                <div class="summary-header">
                                    <span class="summary-title">多模型推理结果</span>
                                </div>
                                <div class="summary-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">成功模型:</span>
                                        <span class="stat-value">{{ multiModelResults.successful_models || 0 }} / {{ multiModelResults.total_models || 0 }}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">总检测数:</span>
                                        <span class="stat-value">{{ multiModelResults.total_objects_detected || 0 }}</span>
                                    </div>
                                </div>
                            </div> -->

                            <!-- 模型结果对比 -->
                            <div class="model-comparison">
                                <div v-for="(modelResult, index) in multiModelResults.model_results"
                                     :key="index" class="model-result-panel">
                                    <div class="model-header">
                                        <span class="model-name">{{ formatModelName(modelResult.model_name) }}</span>
                                        <span class="model-status" :class="{ 'success': modelResult.success, 'error': !modelResult.success }">
                                            {{ modelResult.success ? '推理成功' : '推理失败' }}
                                        </span>
                                    </div>

                                    <div v-if="modelResult.success" class="model-content">
                                        <!-- 模型推理结果图片 -->
                                        <div v-if="modelResult.image_data" class="model-image-container">
                                            <img :src="`data:image/${modelResult.image_format || 'jpeg'};base64,${modelResult.image_data}`"
                                                 alt="模型推理结果" class="model-result-image">
                                            <div class="model-zoom-icon"
                                                 @click="openImagePreview(`data:image/${modelResult.image_format || 'jpeg'};base64,${modelResult.image_data}`)">
                                                <q-icon name="zoom_in" size="0.6rem" color="white" />
                                            </div>
                                        </div>

                                        <!-- 模型检测统计 -->
                                        <div class="model-detection-summary">
                                            <div class="detection-count">
                                                <span class="count-label">检测数量:</span>
                                                <span class="count-value">{{ modelResult.detection_count || 0 }}</span>
                                            </div>
                                            <div v-if="modelResult.detection_summary && modelResult.detection_summary.class_details && modelResult.detection_summary.class_details.length > 0"
                                                 class="class-details">
                                                <div class="class-header">类别分布:</div>
                                                <div v-for="classDetail in modelResult.detection_summary.class_details"
                                                     :key="classDetail.class_name" class="class-item">
                                                    <span class="class-name">{{ classDetail.class_name }}</span>
                                                    <span class="class-count">{{ classDetail.count }}个</span>
                                                </div>
                                            </div>
                                            <!-- 备用方案：使用class_counts显示类别统计 -->
                                            <div v-else-if="modelResult.class_counts && Object.keys(modelResult.class_counts).length > 0"
                                                 class="class-details">
                                                <div class="class-header">类别分布:</div>
                                                <div v-for="(count, className) in modelResult.class_counts"
                                                     :key="className" class="class-item">
                                                    <span class="class-name">{{ className }}</span>
                                                    <span class="class-count">{{ count }}个</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 推理日志展示 -->
                                        <div v-if="modelResult.inference_log" class="model-inference-log">
                                            <div class="log-header">
                                                <span class="log-title">推理日志</span>
                                                <q-btn flat dense size="sm" icon="expand_more"
                                                       @click="toggleLogExpanded(index)"
                                                       :class="{ 'rotated': expandedLogs[index] }">
                                                </q-btn>
                                            </div>
                                            <div v-if="expandedLogs[index]" class="log-content">
                                                <pre class="log-text">{{ modelResult.inference_log }}</pre>
                                            </div>
                                        </div>
                                    </div>

                                    <div v-else class="model-error">
                                        <span class="error-message">{{ modelResult.error || '推理失败' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 批量推理结果 - 当有多张图片且是多模型推理时显示批量视图 -->
                        <div v-if="showResult && !inferenceLoading && multiModelResults && multiModelResults.inference_type === 'multi_model' && uploadedImages.length > 1"
                            class="batch-result-display">
                            <div class="batch-summary">
                                <div class="summary-item">
                                    <span class="summary-label">处理图片:</span>
                                    <span class="summary-value">{{ uploadedImages.length }} / {{ uploadedImages.length }}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">成功模型:</span>
                                    <span class="summary-value">{{ multiModelResults.successful_models || 0 }} / {{ multiModelResults.total_models || 0 }}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">总检测数:</span>
                                    <span class="summary-value">{{ multiModelResults.total_objects_detected || 0 }}</span>
                                </div>
                            </div>

                            <!-- 移除整体检测统计信息，因为现在直接在上面的summary中显示 -->

                            <!-- 显示每个模型的推理结果 -->
                            <div class="batch-models-grid">
                                <div v-for="(modelResult, modelIndex) in multiModelResults.model_results" :key="modelIndex"
                                    class="batch-model-panel">
                                    <div class="batch-model-header">
                                        <span class="batch-model-name">{{ formatModelName(modelResult.model_name) }}</span>
                                        <span class="batch-model-status" :class="{ 'success': modelResult.success, 'error': !modelResult.success }">
                                            {{ modelResult.success ? '推理成功' : '推理失败' }}
                                        </span>
                                    </div>

                                    <div v-if="modelResult.success" class="batch-model-content">
                                        <div v-if="modelResult.image_data" class="batch-image-container">
                                            <img :src="`data:image/${modelResult.image_format || 'jpeg'};base64,${modelResult.image_data}`"
                                                alt="批量推理结果" class="batch-result-image">
                                            <div class="batch-zoom-icon"
                                                @click.stop="openImagePreview(`data:image/${modelResult.image_format || 'jpeg'};base64,${modelResult.image_data}`)">
                                                <q-icon name="zoom_in" size="0.6rem" color="white" />
                                            </div>
                                        </div>
                                        <div class="batch-result-info">
                                            <span class="detection-count">检测: {{ modelResult.detection_count || 0 }}</span>
                                        </div>
                                    </div>

                                    <div v-else class="batch-model-error">
                                        <span class="error-message">{{ modelResult.error || '推理失败' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 模型对比详情 -->
                            <div class="model-comparison-detail">
                                <div class="detail-header">
                                    <span class="detail-title">模型对比详情</span>
                                </div>
                                <div class="models-comparison-grid">
                                    <div v-for="(modelResult, index) in multiModelResults.model_results" :key="index"
                                        class="model-comparison-item">
                                        <div class="model-comparison-header">
                                            <span class="model-name">{{ formatModelName(modelResult.model_name) }}</span>
                                            <span class="detection-count">检测: {{ modelResult.detection_count || 0 }}</span>
                                        </div>

                                        <div v-if="modelResult.success" class="model-comparison-content">
                                            <!-- 类别统计 -->
                                            <div v-if="modelResult.detection_summary && modelResult.detection_summary.class_details && modelResult.detection_summary.class_details.length > 0"
                                                class="model-class-summary">
                                                <div class="class-summary-header">类别分布:</div>
                                                <div v-for="classDetail in modelResult.detection_summary.class_details"
                                                    :key="classDetail.class_name" class="class-summary-item">
                                                    <span class="class-name">{{ classDetail.class_name }}</span>
                                                    <span class="class-count">{{ classDetail.count }}个</span>
                                                </div>
                                            </div>

                                            <!-- 备用方案：使用class_counts -->
                                            <div v-else-if="modelResult.class_counts && Object.keys(modelResult.class_counts).length > 0"
                                                class="model-class-summary">
                                                <div class="class-summary-header">类别分布:</div>
                                                <div v-for="(count, className) in modelResult.class_counts"
                                                    :key="className" class="class-summary-item">
                                                    <span class="class-name">{{ className }}</span>
                                                    <span class="class-count">{{ count }}个</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div v-else class="model-comparison-error">
                                            <span class="error-text">{{ modelResult.error || '推理失败' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下一步按钮 -->
        <div class="next">
            <q-btn class="prevBtn roundBox" color="grey-7" label="上一步" @click="prevStep" />
            <q-btn class="nextBtn roundBox" color="primary" label="完成" @click="nextStep" />
        </div>
    </div>

    <!-- 图片预览对话框 -->
    <q-dialog v-model="imagePreviewVisible" class="image-preview-dialog">
        <q-card class="image-preview-card">
            <q-card-section class="image-preview-header">
                <div class="preview-title">图片预览</div>
                <q-btn flat round dense icon="close" @click="imagePreviewVisible = false" class="close-btn" />
            </q-card-section>

            <q-card-section class="image-preview-content">
                <img :src="previewImageSrc" alt="预览图片" class="preview-image">
            </q-card-section>
        </q-card>
    </q-dialog>
</template>

<script setup>
import { ref, defineEmits, onMounted, computed } from 'vue'
import { usePlugin } from 'src/composables/plugin.js'
import { api } from 'boot/axios'

// 定义事件
const emit = defineEmits(['next-step', 'prev-step'])

// 引入通知插件
const { notify, localStorage: quasarLocalStorage } = usePlugin()

// 上传的图片列表
const uploadedImages = ref([])

// 多模型推理结果
const multiModelResults = ref(null)

// 训练任务ID - 从localStorage获取
const trainingTaskId = ref(null)

// 是否显示运行结果
const showResult = ref(false)

// 推理加载状态
const inferenceLoading = ref(false)

// 批量推理结果
const batchResults = ref({})

// 移除不再需要的selectedBatchIndex，因为现在使用多模型推理结果显示

// 图片预览相关
const imagePreviewVisible = ref(false)
const previewImageSrc = ref('')

// 模型选项
const modelOptions = ref([])

// 推理日志展开状态
const expandedLogs = ref({})

// 获取训练任务ID
function getTrainingTaskId() {
    try {
        // 尝试从localStorage获取
        const taskId = localStorage.getItem('currentTrainingTaskId') ||
            sessionStorage.getItem('currentTrainingTaskId')

        if (taskId) {
            console.log('从存储中获取到训练任务ID:', taskId)
            return taskId
        }
    } catch (error) {
        console.warn('获取训练任务ID失败:', error)
    }

    return null
}

// 获取模型列表
async function fetchModelOptions() {
    try {
        // 获取训练任务ID
        const taskId = getTrainingTaskId()

        if (!taskId) {
            console.log('未找到训练任务ID，不显示任何模型选项')
            modelOptions.value = []
            return
        }

        console.log('获取模型列表，任务ID:', taskId)

        // 根据任务ID和转换状态查询模型列表 - 只查询已转换为OM格式的模型
        const response = await api.get(`backend/training/models?task_id=${taskId}`)
        console.log('模型列表API响应:', response)

        // 兼容多种响应格式
        let modelData = null
        if (response && response.data && Array.isArray(response.data)) {
            // 格式1: response.data 直接是数组
            modelData = response.data
        } else if (response && response.success && response.data && Array.isArray(response.data)) {
            // 格式2: response.success + response.data
            modelData = response.data
        } else if (response && Array.isArray(response)) {
            // 格式3: response 直接是数组
            modelData = response
        }

        if (modelData && modelData.length > 0) {
            // 处理API返回的模型数据
            modelOptions.value = modelData.map(model => ({
                label: model.model_name + '.om ' || `模型 ${model.id}`,
                value: model.id
            }))
            console.log(`成功获取到 ${modelOptions.value.length} 个已转换的模型`)
        } else {
            console.log(`任务ID ${taskId} 暂无已转换为OM格式的模型`)
            modelOptions.value = []
        }
    } catch (error) {
        console.error('获取模型列表失败:', error)

        // 获取训练任务ID用于错误提示
        const taskId = getTrainingTaskId()

        if (!taskId) {
            console.log('无训练任务ID，不显示模型选项')
            modelOptions.value = []
        } else {
            console.log('API调用失败，不显示模型选项')
            modelOptions.value = []
        }
    }
}

// 处理文件上传
const handleFileUpload = (uploadFile, uploadFiles) => {
    // 如果是多选上传，处理所有新增的文件
    const filesToProcess = uploadFiles ? uploadFiles.slice(-1) : [uploadFile]

    let successCount = 0

    filesToProcess.forEach(file => {
        if (file && file.raw) {
            // 验证文件类型
            const allowedTypes = /\.(jpeg|jpg|png|tiff|tif|bmp)$/i
            if (!allowedTypes.test(file.name)) {
                notify(`文件 ${file.name} 格式不支持，请上传 .jpeg/.jpg/.png/.tiff/.tif/.bmp 格式的图片`, 'negative')
                return
            }

            // 验证文件大小 (100MB)
            if (file.raw.size / 1024 / 1024 > 100) {
                notify(`文件 ${file.name} 大小不能超过100MB`, 'negative')
                return
            }

            // 验证文件数量
            if (uploadedImages.value.length >= 8) {
                notify('最多只能上传8个文件', 'negative')
                return
            }

            // 检查是否已经上传过相同文件
            const exists = uploadedImages.value.some(img => img.name === file.name)
            if (exists) {
                notify(`文件 ${file.name} 已存在`, 'negative')
                return
            }

            // 创建预览URL并添加到列表
            const imageItem = {
                name: file.name,
                url: URL.createObjectURL(file.raw),
                raw: file.raw
            }
            uploadedImages.value.push(imageItem)
            successCount++
            console.log('文件上传成功:', file.name)
        }
    })

    if (successCount > 0) {
        notify(`成功上传 ${successCount} 个文件`, 'positive')
    }
}

// 删除上传的图片
const removeUploadedImage = (index) => {
    if (uploadedImages.value[index]) {
        URL.revokeObjectURL(uploadedImages.value[index].url)
        uploadedImages.value.splice(index, 1)
    }
    // 如果删除后没有图片了，隐藏结果
    if (uploadedImages.value.length === 0) {
        showResult.value = false
    }
}

// 重置所有
const resetAll = () => {
    // 清空所有上传的图片
    uploadedImages.value.forEach(image => {
        URL.revokeObjectURL(image.url)
    })
    uploadedImages.value = []
    showResult.value = false
    inferenceLoading.value = false

    // 重置多模型推理相关状态
    multiModelResults.value = null
    batchResults.value = {}

    // 重置检测统计信息
    singleDetectionSummary.value = null

    // 重置推理日志展开状态
    expandedLogs.value = {}

    console.log('已重置所有内容')
}

// 结果数据
const resultImageSrc = ref('../../assets/images/demoImage.png')
const inferenceTime = ref('15ms')
const memoryUsage = ref('1.2GB')

// 新增：检测统计信息
const singleDetectionSummary = ref(null)

// 计算属性
const canRunInference = computed(() => {
    // 检查是否有可用的模型
    if (modelOptions.value.length === 0) return false
    // 检查是否上传了图片
    return uploadedImages.value.length > 0
})

// 移除不再需要的selectedBatchResult计算属性，因为现在使用多模型推理结果

// 移除不再需要的totalDetections和averageInferenceTime计算属性，因为现在直接使用multiModelResults中的数据

// 轮询检查推理状态
const pollInferenceStatus = async (inferenceLogId, maxAttempts = 60, interval = 3000) => {
    let attempts = 0

    while (attempts < maxAttempts) {
        try {
            console.log(`轮询推理状态，第 ${attempts + 1} 次尝试，推理日志ID: ${inferenceLogId}`)

            const response = await api.get(`http://127.0.0.1:8000/backend/training/models/inference/${inferenceLogId}/status`)
            console.log('推理状态响应:', response)

            // 检查推理是否完成
            if (response && response.success === true && response.result) {
                const result = response.result

                // 检查状态
                if (result.status === 'completed') {
                    console.log('推理完成，返回结果')
                    return response
                } else if (result.status === 'failed') {
                    throw new Error(result.message || '推理失败')
                } else if (result.status === 'processing') {
                    console.log('推理仍在进行中，继续等待...')
                    // 继续轮询，不显示任何提示
                } else {
                    console.log(`未知状态: ${result.status}，继续等待...`)
                }
            } else if (response && response.success === false) {
                throw new Error(response.message || '推理失败')
            }

            // 等待指定时间后继续轮询
            await new Promise(resolve => setTimeout(resolve, interval))
            attempts++

        } catch (error) {
            console.error('轮询推理状态失败:', error)

            // 如果是网络错误，继续重试
            if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                console.log('网络超时，继续重试...')
                await new Promise(resolve => setTimeout(resolve, interval))
                attempts++
                continue
            }

            // 其他错误直接抛出
            throw error
        }
    }

    // 超时
    throw new Error('推理超时，请稍后重试')
}

// 运行测试
const runTest = async () => {
    if (uploadedImages.value.length === 0) {
        notify('请先上传测试文件', 'warning')
        return
    }

    if (modelOptions.value.length === 0) {
        notify('暂无可用模型', 'warning')
        return
    }

    try {
        // 显示加载状态
        inferenceLoading.value = true
        showResult.value = false

        // 运行多模型推理
        await runMultiModelInference()

    } catch (error) {
        console.error('推理失败:', error)
        notify(error.message || '推理失败', 'negative')
        inferenceLoading.value = false
    }
}

// 多模型推理
const runMultiModelInference = async () => {
    try {
        console.log('准备进行多模型推理，模型数量:', modelOptions.value.length)

        // 统一处理多模型推理，不再区分单张和批量
        await runUnifiedMultiModelInference()

    } catch (error) {
        console.error('多模型推理失败:', error)
        throw error
    }
}

// 统一多模型推理处理
const runUnifiedMultiModelInference = async () => {
    // 获取训练任务ID
    const taskId = getTrainingTaskId()
    if (!taskId) {
        throw new Error('未找到训练任务ID')
    }

    // 准备图片数据
    const imageDataList = []

    for (const image of uploadedImages.value) {
        let imageData = image.url

        if (imageData.startsWith('blob:')) {
            try {
                const response = await fetch(imageData)
                const blob = await response.blob()

                const reader = new FileReader()
                imageData = await new Promise((resolve) => {
                    reader.onloadend = () => resolve(reader.result)
                    reader.readAsDataURL(blob)
                })

                imageData = imageData.split(',')[1]
            } catch (error) {
                console.error('图片转换失败:', error)
                notify('图片转换失败', 'negative')
                inferenceLoading.value = false
                return
            }
        }

        imageDataList.push(imageData)
    }

    console.log('运行多模型推理，任务ID:', taskId, '图片数量:', imageDataList.length, '模型数量:', modelOptions.value.length)

    // 构建请求参数 - 根据图片数量决定使用单张还是批量接口
    const requestData = {
        task_id: taskId,
        confidence_threshold: 0.5,
        device_id: 0
    }

    // 如果只有一张图片，使用 input_source；多张图片使用 input_sources
    if (imageDataList.length === 1) {
        requestData.input_source = imageDataList[0]
    } else {
        requestData.input_sources = imageDataList
    }

    try {
        // 发送推理请求
        const response = await api.post('backend/training/models/inference', requestData, {
            timeout: 600000 // 10分钟超时
        })

        console.log('多模型推理响应:', response)

        if (response && response.success === true) {
            console.log(`推理成功，获得 ${response.model_results?.length} 个模型结果`)

            // 保存多模型推理结果
            multiModelResults.value = response

            // 重置推理日志展开状态
            expandedLogs.value = {}

            showResult.value = true
            inferenceLoading.value = false

            notify(`多模型推理完成，成功推理 ${response.successful_models} / ${response.total_models} 个模型，共 ${response.model_results?.length} 个结果`, 'positive')
        } else {
            console.error('推理响应格式错误:', response)
            throw new Error(response.error || response.message || '多模型推理失败')
        }

    } catch (error) {
        console.error('多模型推理请求失败:', error)
        inferenceLoading.value = false
        throw new Error(error.response?.error || error.message || '多模型推理失败')
    }
}

// 单张图片多模型推理（保留作为备用）
const runSingleImageMultiModel = async () => {
    // 获取训练任务ID
    const taskId = getTrainingTaskId()
    if (!taskId) {
        throw new Error('未找到训练任务ID')
    }

    // 获取第一张图片作为测试输入
    const testImage = uploadedImages.value[0]

    // 将图片转换为base64格式
    let imageData = testImage.url

    // 如果URL是Blob URL，需要转换为base64
    if (imageData.startsWith('blob:')) {
        try {
            const response = await fetch(imageData)
            const blob = await response.blob()

            // 将Blob转换为base64
            const reader = new FileReader()
            imageData = await new Promise((resolve) => {
                reader.onloadend = () => resolve(reader.result)
                reader.readAsDataURL(blob)
            })

            // 移除base64前缀，只保留数据部分
            imageData = imageData.split(',')[1]
        } catch (error) {
            console.error('图片转换失败:', error)
            notify('图片转换失败', 'negative')
            inferenceLoading.value = false
            return
        }
    }

    console.log('运行单张图片多模型推理，任务ID:', taskId, '模型数量:', modelOptions.value.length)

    // 构建请求参数
    const requestData = {
        task_id: taskId,
        input_source: imageData,
        confidence_threshold: 0.5,
        device_id: 0
    }

    try {
        // 发送推理请求
        const response = await api.post('backend/training/models/inference', requestData, {
            timeout: 300000 // 5分钟超时
        })

        console.log('单张多模型推理响应:', response)

        if (response && response.success) {
            // 保存多模型推理结果
            multiModelResults.value = response

            // 重置推理日志展开状态
            expandedLogs.value = {}

            showResult.value = true
            inferenceLoading.value = false

            notify(`单张多模型推理完成，成功推理 ${response.successful_models} / ${response.total_models} 个模型`, 'positive')
        } else {
            throw new Error(response.error || '单张多模型推理失败')
        }

    } catch (error) {
        console.error('单张多模型推理请求失败:', error)
        inferenceLoading.value = false
        throw new Error(error.response?.error || error.message || '单张多模型推理失败')
    }
}

// 批量图片多模型推理
const runBatchImageMultiModel = async () => {
    // 获取训练任务ID
    const taskId = getTrainingTaskId()
    if (!taskId) {
        throw new Error('未找到训练任务ID')
    }

    // 准备批量图片数据
    const imageDataList = []

    for (const image of uploadedImages.value) {
        let imageData = image.url

        if (imageData.startsWith('blob:')) {
            try {
                const response = await fetch(imageData)
                const blob = await response.blob()

                const reader = new FileReader()
                imageData = await new Promise((resolve) => {
                    reader.onloadend = () => resolve(reader.result)
                    reader.readAsDataURL(blob)
                })

                imageData = imageData.split(',')[1]
            } catch (error) {
                console.error('图片转换失败:', error)
                notify('图片转换失败', 'negative')
                inferenceLoading.value = false
                return
            }
        }

        imageDataList.push(imageData)
    }

    console.log('运行批量图片多模型推理，任务ID:', taskId, '图片数量:', imageDataList.length, '模型数量:', modelOptions.value.length)

    // 构建请求参数
    const requestData = {
        task_id: taskId,
        input_sources: imageDataList,
        confidence_threshold: 0.5,
        device_id: 0
    }

    try {
        // 发送批量推理请求
        const response = await api.post('backend/training/models/inference', requestData, {
            timeout: 600000 // 10分钟超时
        })

        console.log('批量多模型推理响应:', response)

        if (response && response.success === true) {
            console.log('批量推理成功，处理结果数据')
            // 保存批量推理结果（现在统一使用multiModelResults）
            multiModelResults.value = response

            // 重置推理日志展开状态
            expandedLogs.value = {}

            showResult.value = true
            inferenceLoading.value = false

            notify(`批量多模型推理完成，成功推理 ${response.successful_models} / ${response.total_models} 个模型`, 'positive')
        } else {
            console.error('批量推理响应格式错误:', response)
            throw new Error(response.error || response.message || '批量多模型推理失败')
        }

    } catch (error) {
        console.error('批量多模型推理请求失败:', error)
        inferenceLoading.value = false
        throw new Error(error.response?.error || error.message || '批量多模型推理失败')
    }
}

// 单张推理
const runSingleInference = async () => {
    // 获取第一张图片作为测试输入
    const testImage = uploadedImages.value[0]

    // 将图片转换为base64格式
    let imageData = testImage.url

    // 如果URL是Blob URL，需要转换为base64
    if (imageData.startsWith('blob:')) {
        try {
            const response = await fetch(imageData)
            const blob = await response.blob()

            // 将Blob转换为base64
            const reader = new FileReader()
            imageData = await new Promise((resolve) => {
                reader.onloadend = () => resolve(reader.result)
                reader.readAsDataURL(blob)
            })

            // 移除base64前缀，只保留数据部分
            imageData = imageData.split(',')[1]
        } catch (error) {
            console.error('图片转换失败:', error)
            notify('图片转换失败', 'negative')
            inferenceLoading.value = false
            return
        }
    }

    console.log('运行单张推理，模型ID:', selectedModel.value, '图片数据已准备')

    // 构建请求参数
    const requestData = {
        model_id: selectedModel.value,
        input_source: imageData,
        confidence_threshold: 0.5,
        iou_threshold: 0.45,
        max_detections: 1000,
        save_result: true,
        return_image: true
    }

    console.log('单张推理请求参数:', {
        ...requestData,
        input_source: '(base64数据已省略)'
    })

    // 调用统一推理API启动任务
    const startResponse = await api.post('http://127.0.0.1:8000/backend/training/models/inference', requestData)
    console.log('推理启动响应:', startResponse)

    // 检查启动响应
    if (!startResponse || !startResponse.success || !startResponse.inference_log_id) {
        throw new Error(startResponse?.message || '推理任务启动失败，未返回推理日志ID')
    }

    const inferenceLogId = startResponse.inference_log_id
    console.log('推理任务已启动，推理日志ID:', inferenceLogId)

    // 检查初始状态
    if (startResponse.status === 'processing' || startResponse.result?.status === 'processing') {
        console.log('推理任务正在后台处理中，开始轮询状态...')
        // 静默开始轮询，不显示额外提示
    }

    // 开始轮询推理状态
    const response = await pollInferenceStatus(inferenceLogId)
    // 处理推理结果
    if (response && response.success && response.result) {
        const result = response.result
        console.log('推理结果数据:', result)

        // 显示推理结果
        showResult.value = true

        // 检查推理状态
        if (result.status === 'completed') {
            // 检查是否有图像数据
            if (result.image_data && result.has_image) {
                // 更新结果图片 - 根据后端返回的格式构建完整的data URL
                const imageFormat = result.image_format || 'jpeg'
                resultImageSrc.value = `data:image/${imageFormat};base64,${result.image_data}`

                // 更新推理信息
                if (result.inference_time_ms !== undefined) {
                    inferenceTime.value = `${result.inference_time_ms}ms`
                } else {
                    inferenceTime.value = '15ms'
                }

                // 更新检测统计信息
                if (result.detection_summary) {
                    singleDetectionSummary.value = result.detection_summary
                    console.log(`检测到 ${result.detection_summary.total_objects || 0} 个目标`)

                    // 显示类别统计
                    if (result.detection_summary.class_details && result.detection_summary.class_details.length > 0) {
                        console.log('类别分布:', result.detection_summary.class_details)
                    }
                } else if (result.detection_count !== undefined) {
                    // 备用方案：使用旧格式
                    singleDetectionSummary.value = {
                        total_objects: result.detection_count,
                        class_counts: result.class_counts || {},
                        class_details: []
                    }

                    // 如果有class_counts，转换为class_details格式
                    if (result.class_counts) {
                        singleDetectionSummary.value.class_details = Object.entries(result.class_counts).map(([className, count]) => ({
                            class_name: className,
                            count: count
                        }))
                    }

                    console.log(`检测到 ${result.detection_count} 个目标`)
                }

                // 显示内存占用（如果后端提供的话，否则使用默认值）
                memoryUsage.value = '1.2GB' // 默认值，可以根据实际情况调整

                notify('推理完成', 'positive')
            } else {
                // 推理完成但没有图像数据
                console.log('推理完成但未返回图像数据:', result.image_message || '未知原因')
                resultImageSrc.value = '../../assets/images/demoImage.png'
                inferenceTime.value = result.inference_time_ms ? `${result.inference_time_ms}ms` : '15ms'
                memoryUsage.value = '1.2GB'
                notify('推理完成，但未返回图像数据', 'info')
            }
        } else {
            // 其他状态（理论上不应该到这里，因为轮询会处理）
            console.warn('推理状态异常:', result.status)
            resultImageSrc.value = '../../assets/images/demoImage.png'
            inferenceTime.value = '15ms'
            memoryUsage.value = '1.2GB'
            notify('推理状态异常', 'warning')
        }
    } else {
        // API调用成功但响应格式不符合预期
        console.warn('推理API响应格式异常:', response)
        showResult.value = true
        resultImageSrc.value = '../../assets/images/demoImage.png'
        inferenceTime.value = '15ms'
        memoryUsage.value = '1.2GB'
        notify('推理完成，显示默认结果', 'info')
    }

    // 关闭加载状态
    inferenceLoading.value = false
}

// 批量推理
const runBatchInference = async () => {
    console.log('开始批量推理，图片数量:', uploadedImages.value.length)

    // 将所有图片转换为base64格式
    const inputSources = []

    for (let i = 0; i < uploadedImages.value.length; i++) {
        const image = uploadedImages.value[i]
        let imageData = image.url

        // 如果URL是Blob URL，需要转换为base64
        if (imageData.startsWith('blob:')) {
            try {
                const response = await fetch(imageData)
                const blob = await response.blob()

                // 将Blob转换为base64
                const reader = new FileReader()
                imageData = await new Promise((resolve) => {
                    reader.onloadend = () => resolve(reader.result)
                    reader.readAsDataURL(blob)
                })

                // 移除base64前缀，只保留数据部分
                imageData = imageData.split(',')[1]
            } catch (error) {
                console.error(`图片 ${i + 1} 转换失败:`, error)
                notify(`图片 ${i + 1} 转换失败`, 'negative')
                inferenceLoading.value = false
                return
            }
        }

        inputSources.push(imageData)
    }

    console.log('批量推理，模型ID:', selectedModel.value, '图片数据已准备，数量:', inputSources.length)

    // 构建批量推理请求参数
    const requestData = {
        model_id: selectedModel.value,
        input_sources: inputSources,
        confidence_threshold: 0.5,
        iou_threshold: 0.45,
        max_detections: 1000,
        save_result: true,
        return_image: true
    }

    console.log('批量推理请求参数:', {
        ...requestData,
        input_sources: `(${inputSources.length}张图片的base64数据已省略)`
    })

    // 调用统一推理API启动任务（自动判断批量）
    const startResponse = await api.post('http://127.0.0.1:8000/backend/training/models/inference', requestData)
    console.log('批量推理启动响应:', startResponse)

    // 检查启动响应
    if (!startResponse || !startResponse.success || !startResponse.inference_log_id) {
        throw new Error(startResponse?.message || '批量推理任务启动失败，未返回推理日志ID')
    }

    const inferenceLogId = startResponse.inference_log_id
    console.log('批量推理任务已启动，推理日志ID:', inferenceLogId)

    // 开始轮询推理状态
    const response = await pollInferenceStatus(inferenceLogId)

    // 处理批量推理结果
    if (response && response.success && response.result) {
        const result = response.result
        console.log('批量推理结果数据:', result)

        // 显示推理结果
        showResult.value = true

        // 检查推理状态
        if (result.status === 'completed') {
            // 现在统一使用multiModelResults，不再需要单独的批量推理结果处理
            // 这个函数现在主要作为备用，实际使用runUnifiedMultiModelInference

            notify(`推理完成`, 'positive')
        } else {
            console.warn('批量推理状态异常:', result.status)
            notify('批量推理状态异常', 'warning')
        }
    } else {
        console.warn('批量推理API响应格式异常:', response)
        notify('批量推理完成，但响应格式异常', 'info')
    }

    // 关闭加载状态
    inferenceLoading.value = false
}

// 移除不再需要的selectBatchResult方法，因为现在使用多模型推理结果显示

// 打开图片预览
const openImagePreview = (imageSrc) => {
    previewImageSrc.value = imageSrc
    imagePreviewVisible.value = true
}

// 切换推理日志展开状态
const toggleLogExpanded = (index) => {
    expandedLogs.value[index] = !expandedLogs.value[index]
}

// 格式化模型名称显示
const formatModelName = (modelName) => {
    if (!modelName) return '未知模型'

    // 检查是否包含 _image_ 后缀
    if (modelName.includes('_image_')) {
        const parts = modelName.split('_image_')
        const baseName = parts[0]
        const imageIndex = parts[1]
        return `${baseName} (图片${imageIndex})`
    }

    return modelName
}

// 保存测试
const saveTest = () => {
    console.log('保存测试结果')
}



// 停止训练任务
async function stopTrainingTask(taskId) {
    try {
        console.log('调用后端停止训练接口，任务ID:', taskId)

        // 获取token
        let token = quasarLocalStorage.getItem('token')
        if (!token) {
            token = localStorage.getItem('token') || sessionStorage.getItem('token')
        }

        // 处理token格式
        if (token && token.startsWith('Bearer ')) {
            token = token.substring(7).trim()
        }

        // 调用停止训练接口 - 使用cancel接口来停止训练并释放容器
        const response = await api.post(`backend/training/${taskId}/cancel`, {}, {
            headers: token ? { 'Authorization': `Bearer ${token}` } : {}
        })

        console.log('停止训练响应:', response)

        if (response && response.success) {
            console.log('训练任务停止成功')

            // 清除保存的任务ID
            clearTrainingTaskId()

            return response
        } else {
            throw new Error(response?.message || '停止训练失败')
        }

    } catch (error) {
        console.error('停止训练任务API调用失败:', error)
        throw error
    }
}

// 清除训练任务ID
function clearTrainingTaskId() {
    trainingTaskId.value = null

    try {
        quasarLocalStorage.remove('currentTrainingTaskId')
        localStorage.removeItem('currentTrainingTaskId')
        sessionStorage.removeItem('currentTrainingTaskId')
    } catch (error) {
        console.warn('清除训练任务ID失败:', error)
    }
}

// 下一步按钮点击事件（完成按钮）
async function nextStep() {
    console.log('StepFive: 完成按钮被点击')

    try {
        // 获取训练任务ID
        const taskId = getTrainingTaskId()

        if (taskId) {
            console.log('开始停止训练任务，任务ID:', taskId)

            // 调用后端停止训练接口
            await stopTrainingTask(taskId)

            notify('训练任务已停止，容器资源已释放', 'positive')
        } else {
            console.log('未找到训练任务ID，直接完成流程')
            notify('流程已完成', 'positive')
        }

        // 发出完成事件
        emit('next-step')

    } catch (error) {
        console.error('停止训练任务失败:', error)
        notify('停止训练任务失败: ' + (error.message || '未知错误'), 'negative')

        // 即使停止失败也允许完成流程
        emit('next-step')
    }
}

// 上一步按钮点击事件
function prevStep() {
    emit('prev-step')
}



// 组件挂载时获取模型列表和训练任务ID
onMounted(() => {
    fetchModelOptions()

    // 尝试获取训练任务ID
    const taskId = getTrainingTaskId()
    if (taskId) {
        console.log('StepFive: 找到训练任务ID:', taskId)
    } else {
        console.log('StepFive: 未找到训练任务ID')
    }
})
</script>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    flex: 1;
    box-sizing: border-box;
    position: relative;

    .top {
        display: flex;
        margin-bottom: .125rem;
        flex: 1;
        min-height: 9.5rem;

        .left {
            width: 40%;
            height: inherit;
            border: .025rem solid #707070;
            background-color: #181a24;
            background-image:
                repeating-linear-gradient(130deg,
                    rgba(255, 255, 255, 0.05) 0px,
                    rgba(255, 255, 255, 0.01) 4px,
                    transparent 1px,
                    transparent 15px);
            padding: .3375rem;
            display: flex;
            flex-direction: column;
            margin-right: .125rem;
            position: relative;

            &::before {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
                z-index: 3;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5);
                z-index: 3;
            }
        }

        .right {
            width: 60%;
            height: inherit;
            border: .025rem solid #707070;
            background-color: #181a24;
            background-image:
                repeating-linear-gradient(130deg,
                    rgba(255, 255, 255, 0.05) 0px,
                    rgba(255, 255, 255, 0.01) 4px,
                    transparent 1px,
                    transparent 15px);
            padding: .3375rem;
            display: flex;
            flex-direction: column;
            position: relative;

            &::before {
                position: absolute;
                content: '';
                left: -0.1rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198);
                border-radius: .125rem;
                z-index: 3;
            }

            &::after {
                position: absolute;
                content: '';
                left: -0.075rem;
                top: 0;
                width: .025rem;
                height: 100%;
                background: rgba(156, 172, 198, .5);
                z-index: 3;
            }
        }
    }
}

// 左侧区域样式
.section {
    margin-bottom: .375rem;

    .section-header {
        margin-bottom: .25rem;

        .section-title {
            color: #4ab4ff;
            font-size: .2rem;
        }
    }
}

.upload-section {
    .upload-list {
        display: flex;
        flex-wrap: wrap;
        gap: .45rem;
        margin-bottom: .1875rem;
    }

    .upload-item {
        width: 1.725rem;
        height: 1.725rem;
        position: relative;
    }

    .test-file-uploader {
        width: 100%;
        height: 100%;

        :deep(.el-upload) {
            width: 100%;
            height: 100%;
            border: .0125rem solid #8c939d;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: var(--el-transition-duration-fast);
            background: #181a24;

            &:hover {
                border-color: #4ab4ff;
            }
        }

        .upload-area {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .upload-icon {
                width: .35rem;
                height: .35rem;
            }
        }
    }

    .image-preview {
        border: 0.0125rem solid #8c939d;
        overflow: hidden;

        .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-btn {
            position: absolute;
            top: 0;
            right: 0;
            width: .45rem;
            height: .45rem;
            cursor: pointer;

            .delete-icon {
                width: 100%;
                height: 100%;
            }
        }
    }

    .upload-tips {
        color: #cea345;
        font-size: .15rem;
        line-height: 1.4;
    }
}

.model-section {
    margin-bottom: .375rem;

    .model-info {
        .model-title {
            color: #4ab4ff;
            font-size: .2rem;
            margin-bottom: .125rem;
            display: block;
        }

        .model-list {
            display: flex;
            flex-direction: column;
            gap: .0625rem;

            .model-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: .125rem .15rem;
                background-color: rgba(74, 180, 255, 0.1);
                border-radius: .0625rem;
                border: .025rem solid rgba(74, 180, 255, 0.3);

                .model-name {
                    color: #fff;
                    font-size: .175rem;
                }

                .model-status {
                    color: #4ab4ff;
                    font-size: .125rem;
                    background-color: rgba(74, 180, 255, 0.2);
                    padding: .0625rem .125rem;
                    border-radius: .03125rem;
                }
            }
        }

        .no-models {
            padding: .15rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: .0625rem;
            border: .025rem solid rgba(255, 255, 255, 0.1);

            .no-models-text {
                color: #999;
                font-size: .175rem;
            }
        }
    }
}



.button-section {
    display: flex;
    gap: .25rem;
    margin-top: auto;

    .action-btn {
        flex: 1;
        height: .5rem !important;
        font-size: .175rem !important;
        padding: 0 .25rem !important;
        min-height: auto !important;
        border-radius: .0625rem;

        &:before {
            display: none !important;
        }

        &.reset-btn {
            background-color: #2b2d37 !important;
            color: #fff !important;

            &:hover {
                background-color: rgba(255, 255, 255, 0.1) !important;
            }
        }

        &.run-btn {
            background-color: #164c82 !important;
            color: #fff !important;

            &:hover {
                background-color: rgba(22, 76, 130, 0.8) !important;
            }
        }

        &.save-btn {
            background-color: #164c82 !important;
            color: #fff !important;

            &:hover {
                background-color: rgba(22, 76, 130, 0.8) !important;
            }
        }
    }
}

// 右侧区域样式
.result-section {
    height: 100%;
    display: flex;
    flex-direction: column;

    .result-header {
        display: flex;
        align-items: center;
        margin-bottom: .25rem;

        .arrow-icon {
            width: .2rem;
            height: .2rem;
            margin-right: .125rem;
        }

        .result-title {
            color: #4ab4ff;
            font-size: .2rem;
        }
    }

    .result-content {
        flex: 1;
        padding: .25rem;
        margin-bottom: .25rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .empty-result {
            .empty-text {
                color: #999;
                font-size: .175rem;
            }
        }

        // 批量推理结果样式
        .batch-result-display {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;

            .batch-summary {
                display: flex;
                gap: .25rem;
                margin-bottom: .25rem;
                padding: .125rem;
                background-color: rgba(255, 255, 255, 0.05);
                border-radius: .0625rem;

                .summary-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    flex: 1;

                    .summary-label {
                        color: #999;
                        font-size: .125rem;
                        margin-bottom: .0625rem;
                    }

                    .summary-value {
                        color: #4ab4ff;
                        font-size: .15rem;
                        font-weight: bold;
                    }
                }
            }

            // 新增：整体检测统计样式
            .overall-detection-summary {
                margin-bottom: .25rem;
                padding: .125rem;
                background-color: rgba(74, 180, 255, 0.1);
                border-radius: .0625rem;
                border: .025rem solid rgba(74, 180, 255, 0.3);

                .overall-summary-header {
                    margin-bottom: .125rem;

                    .overall-summary-title {
                        color: #4ab4ff;
                        font-size: .15rem;
                        font-weight: bold;
                    }
                }

                .overall-summary-content {
                    .overall-stats {
                        margin-bottom: .125rem;

                        .stat-item {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: .0625rem;

                            .stat-label {
                                color: #999;
                                font-size: .125rem;
                            }

                            .stat-value {
                                color: #4ab4ff;
                                font-size: .125rem;
                                font-weight: bold;
                            }
                        }
                    }

                    .overall-class-details {
                        .overall-class-header {
                            color: #999;
                            font-size: .125rem;
                            margin-bottom: .0625rem;
                        }

                        .overall-class-grid {
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(1rem, 1fr));
                            gap: .0625rem;

                            .overall-class-item {
                                display: flex;
                                justify-content: space-between;
                                padding: .0625rem;
                                background-color: rgba(255, 255, 255, 0.05);
                                border-radius: .0625rem;

                                .overall-class-name {
                                    color: #fff;
                                    font-size: .1rem;
                                }

                                .overall-class-count {
                                    color: #4ab4ff;
                                    font-size: .1rem;
                                    font-weight: bold;
                                }
                            }
                        }
                    }
                }
            }

            // 新增：批量模型网格样式
            .batch-models-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(3rem, 1fr));
                gap: .1875rem;
                margin-bottom: .25rem;

                .batch-model-panel {
                    background-color: rgba(255, 255, 255, 0.05);
                    border-radius: .0625rem;
                    border: .025rem solid rgba(255, 255, 255, 0.1);
                    overflow: hidden;

                    .batch-model-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: .0625rem .125rem;
                        background-color: rgba(74, 180, 255, 0.1);
                        border-bottom: .025rem solid rgba(255, 255, 255, 0.1);

                        .batch-model-name {
                            color: #fff;
                            font-size: .125rem;
                            font-weight: bold;
                        }

                        .batch-model-status {
                            font-size: .09375rem;
                            padding: .03125rem .0625rem;
                            border-radius: .03125rem;

                            &.success {
                                color: #4ab4ff;
                                background-color: rgba(74, 180, 255, 0.2);
                            }

                            &.error {
                                color: #ff6b6b;
                                background-color: rgba(255, 107, 107, 0.2);
                            }
                        }
                    }

                    .batch-model-content {
                        padding: .125rem;

                        .batch-image-container {
                            position: relative;
                            margin-bottom: .0625rem;
                            border-radius: .0625rem;
                            overflow: hidden;

                            .batch-result-image {
                                width: 100%;
                                height: auto;
                                max-height: 2rem;
                                object-fit: contain;
                                background-color: rgba(0, 0, 0, 0.3);
                            }

                            .batch-zoom-icon {
                                position: absolute;
                                top: .0625rem;
                                right: .0625rem;
                                width: .1875rem;
                                height: .1875rem;
                                background-color: rgba(0, 0, 0, 0.6);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                cursor: pointer;
                                transition: background-color 0.3s;

                                &:hover {
                                    background-color: rgba(0, 0, 0, 0.8);
                                }
                            }
                        }

                        .batch-result-info {
                            text-align: center;

                            .detection-count {
                                color: #4ab4ff;
                                font-size: .09375rem;
                                font-weight: bold;
                            }
                        }
                    }

                    .batch-model-error {
                        padding: .125rem;
                        text-align: center;

                        .error-message {
                            color: #ff6b6b;
                            font-size: .09375rem;
                        }
                    }
                }
            }

            // 新增：模型对比详情样式
            .model-comparison-detail {
                margin-top: .25rem;
                border: .025rem solid rgba(255, 255, 255, 0.1);
                border-radius: .0625rem;
                background-color: rgba(255, 255, 255, 0.05);

                .detail-header {
                    padding: .125rem .1875rem;
                    background-color: rgba(74, 180, 255, 0.1);
                    border-bottom: .025rem solid rgba(255, 255, 255, 0.1);

                    .detail-title {
                        color: #4ab4ff;
                        font-size: .15rem;
                        font-weight: bold;
                    }
                }

                .models-comparison-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(2.5rem, 1fr));
                    gap: .125rem;
                    padding: .1875rem;

                    .model-comparison-item {
                        background-color: rgba(255, 255, 255, 0.05);
                        border-radius: .0625rem;
                        border: .025rem solid rgba(255, 255, 255, 0.1);
                        padding: .125rem;

                        .model-comparison-header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: .0625rem;
                            padding-bottom: .0625rem;
                            border-bottom: .0125rem solid rgba(255, 255, 255, 0.1);

                            .model-name {
                                color: #4ab4ff;
                                font-size: .125rem;
                                font-weight: bold;
                            }

                            .detection-count {
                                color: #cea345;
                                font-size: .1rem;
                            }
                        }

                        .model-comparison-content {
                            .model-class-summary {
                                .class-summary-header {
                                    color: #9cacc6;
                                    font-size: .1rem;
                                    margin-bottom: .0625rem;
                                }

                                .class-summary-item {
                                    display: flex;
                                    justify-content: space-between;
                                    margin-bottom: .03125rem;
                                    padding: .03125rem .0625rem;
                                    background-color: rgba(74, 180, 255, 0.1);
                                    border-radius: .03125rem;

                                    .class-name {
                                        color: #fff;
                                        font-size: .09375rem;
                                    }

                                    .class-count {
                                        color: #4ab4ff;
                                        font-size: .09375rem;
                                        font-weight: bold;
                                    }
                                }
                            }
                        }

                        .model-comparison-error {
                            text-align: center;
                            padding: .0625rem;

                            .error-text {
                                color: #ff6b6b;
                                font-size: .09375rem;
                            }
                        }
                    }
                }
            }

            .batch-results-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(1.5rem, 1fr));
                gap: .125rem;
                max-height: 4rem;
                overflow-y: auto;
                margin-bottom: .25rem;

                .batch-result-item {
                    position: relative;
                    border: .025rem solid #444;
                    border-radius: .0625rem;
                    overflow: hidden;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    &:hover {
                        border-color: #4ab4ff;
                        transform: scale(1.02);
                    }

                    &.selected {
                        border-color: #4ab4ff;
                        box-shadow: 0 0 .125rem rgba(74, 180, 255, 0.5);
                    }

                    .batch-image-container {
                        position: relative;
                        width: 100%;
                        height: 1.2rem;

                        .batch-result-image {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }

                        .batch-zoom-icon {
                            position: absolute;
                            bottom: .0625rem;
                            right: .0625rem;
                            width: .3rem;
                            height: .3rem;
                            background: rgba(0, 0, 0, 0.7);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            opacity: 0;

                            &:hover {
                                background: rgba(74, 180, 255, 0.8);
                                transform: scale(1.1);
                            }
                        }

                        &:hover .batch-zoom-icon {
                            opacity: 1;
                        }
                    }

                    .batch-result-placeholder {
                        width: 100%;
                        height: 1.2rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background-color: #333;
                        color: #999;
                        font-size: .125rem;
                    }

                    .batch-result-info {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        right: 0;
                        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
                        padding: .0625rem;

                        .detection-count {
                            color: #4ab4ff;
                            font-size: .1rem;
                        }
                    }
                }
            }

            .selected-result-detail {
                flex: 1;
                border: .025rem solid #444;
                border-radius: .0625rem;
                overflow: hidden;

                .detail-header {
                    background-color: rgba(74, 180, 255, 0.1);
                    padding: .125rem;
                    border-bottom: .025rem solid #444;

                    .detail-title {
                        color: #4ab4ff;
                        font-size: .15rem;
                        font-weight: bold;
                    }
                }

                .detail-content {
                    padding: .125rem;
                    height: calc(100% - .375rem);
                    display: flex;
                    gap: .125rem;

                    .detail-image-container {
                        position: relative;
                        width: 2rem;
                        height: 1.5rem;

                        .detail-image {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            border-radius: .0625rem;
                            border: .025rem solid #444;
                        }

                        .detail-zoom-icon {
                            position: absolute;
                            bottom: .0625rem;
                            right: .0625rem;
                            width: .325rem;
                            height: .325rem;
                            background: rgba(0, 0, 0, 0.7);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            opacity: 0;

                            &:hover {
                                background: rgba(74, 180, 255, 0.8);
                                transform: scale(1.1);
                            }
                        }

                        &:hover .detail-zoom-icon {
                            opacity: 1;
                        }
                    }

                    .detail-info {
                        flex: 1;

                        .detail-item {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: .0625rem;

                            .detail-label {
                                color: #999;
                                font-size: .125rem;
                            }

                            .detail-value {
                                color: #4ab4ff;
                                font-size: .125rem;
                                font-weight: bold;
                            }
                        }

                        // 新增：详细检测统计样式
                        .detail-detection-summary {
                            margin-top: .125rem;

                            .detail-summary-header {
                                color: #999;
                                font-size: .125rem;
                                margin-bottom: .0625rem;
                            }

                            .detail-class-item {
                                display: flex;
                                justify-content: space-between;
                                margin-bottom: .0625rem;
                                padding: .0625rem;
                                background-color: rgba(74, 180, 255, 0.1);
                                border-radius: .0625rem;

                                .detail-class-name {
                                    color: #fff;
                                    font-size: .1rem;
                                }

                                .detail-class-count {
                                    color: #4ab4ff;
                                    font-size: .1rem;
                                    font-weight: bold;
                                }
                            }
                        }

                        .detections-list {
                            margin-top: .125rem;

                            .detections-header {
                                color: #999;
                                font-size: .125rem;
                                margin-bottom: .0625rem;
                            }

                            .detection-item {
                                display: flex;
                                justify-content: space-between;
                                margin-bottom: .0625rem;
                                padding: .0625rem;
                                background-color: rgba(255, 255, 255, 0.05);
                                border-radius: .0625rem;

                                .detection-class {
                                    color: #fff;
                                    font-size: .1rem;
                                }

                                .detection-confidence {
                                    color: #4ab4ff;
                                    font-size: .1rem;
                                }
                            }

                            .more-detections {
                                color: #999;
                                font-size: .1rem;
                                text-align: center;
                                margin-top: .0625rem;
                            }
                        }
                    }
                }
            }
        }

        .loading-result {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .loading-spinner {
                margin-bottom: .25rem;
            }

            .loading-text {
                color: #4ab4ff;
                font-size: .2rem;
                margin-bottom: .125rem;
                font-weight: 500;
            }

            .loading-tips {
                color: #999;
                font-size: .15rem;
                text-align: center;
                line-height: 1.4;
            }
        }

        .result-display {
            display: flex;
            flex-direction: column;
            align-items: center;

            .image-container {
                position: relative;
                margin-bottom: .1875rem;

                .result-image {
                    width: auto;
                    max-width: 100%;
                    height: auto;
                    max-height: 6rem;
                    object-fit: contain;
                    border: .025rem solid #333;
                    border-radius: .0625rem;
                    display: block;
                }

                .zoom-icon {
                    position: absolute;
                    bottom: .0625rem;
                    right: .0625rem;
                    width: .375rem;
                    height: .375rem;
                    background: rgba(0, 0, 0, 0.7);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    opacity: 0.8;

                    &:hover {
                        background: rgba(74, 180, 255, 0.8);
                        opacity: 1;
                        transform: scale(1.1);
                    }
                }
            }

            .result-info {
                display: flex;
                flex-direction: column;
                gap: .25rem;

                .info-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .info-label {
                        color: #4ab4ff;
                        font-size: .15rem;
                    }

                    .info-value {
                        color: #cea345;
                        font-size: .175rem;
                        font-weight: bold;
                    }
                }

                // 新增：检测统计信息样式
                .detection-summary {
                    margin-top: .25rem;
                    padding: .125rem;
                    background-color: rgba(74, 180, 255, 0.1);
                    border-radius: .0625rem;
                    border: .025rem solid rgba(74, 180, 255, 0.3);

                    .summary-header {
                        margin-bottom: .125rem;

                        .summary-title {
                            color: #4ab4ff;
                            font-size: .15rem;
                            font-weight: bold;
                        }
                    }

                    .summary-content {
                        .total-count {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: .125rem;

                            .count-label {
                                color: #999;
                                font-size: .125rem;
                            }

                            .count-value {
                                color: #4ab4ff;
                                font-size: .125rem;
                                font-weight: bold;
                            }
                        }

                        .class-details {
                            .class-header {
                                color: #999;
                                font-size: .125rem;
                                margin-bottom: .0625rem;
                            }

                            .class-item {
                                display: flex;
                                justify-content: space-between;
                                margin-bottom: .0625rem;
                                padding: .0625rem;
                                background-color: rgba(255, 255, 255, 0.05);
                                border-radius: .0625rem;

                                .class-name {
                                    color: #fff;
                                    font-size: .1rem;
                                }

                                .class-count {
                                    color: #4ab4ff;
                                    font-size: .1rem;
                                    font-weight: bold;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 底部按钮
    .bottom {
        display: flex;
        align-items: center;
        flex-shrink: 0;


    }
}

// 通用样式
.labelColor {
    color: #4ab4ff;
}

// 图片预览对话框样式
.image-preview-dialog {
    :deep(.q-dialog__inner) {
        padding: 2rem;
    }
}

.image-preview-card {
    background-color: #1a1a1a;
    color: #fff;
    max-width: 70vw;
    max-height: 70vh;
    border-radius: .125rem;
    box-shadow: 0 .25rem 1rem rgba(0, 0, 0, 0.5);

    .image-preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: .125rem .25rem;
        border-bottom: .025rem solid #444;
        background-color: #2a2a2a;
        border-radius: .125rem .125rem 0 0;

        .preview-title {
            color: #4ab4ff;
            font-size: .175rem;
            font-weight: 500;
        }

        .close-btn {
            color: #fff;

            &:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
        }
    }

    .image-preview-content {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: .375rem;
        background-color: #1a1a1a;
        border-radius: 0 0 .125rem .125rem;

        .preview-image {
            max-width: 100%;
            max-height: 50vh;
            object-fit: contain;
            border-radius: .0625rem;
            box-shadow: 0 .125rem .5rem rgba(0, 0, 0, 0.3);
        }
    }
}



// fix small screnen bug
@media screen and (max-width: 1528px) {
    :deep(.q-field__marginal) {
        height: unset !important;
    }
}

.next {
    display: flex;
    justify-content: space-between;
    width: 10%;
    gap: .25rem;
    position: absolute;
    right: 0;
    top: -4vh;

    .prevBtn {
        margin-right: auto;
    }

    .nextBtn {
        margin-left: auto;
    }
}

// 多模型推理结果样式
.multi-model-result-display {
    display: flex;
    flex-direction: column;
    gap: .25rem;
    height: 100%;

    .overall-summary {
        padding: .15rem;
        background-color: rgba(74, 180, 255, 0.1);
        border-radius: .0625rem;
        border: .025rem solid rgba(74, 180, 255, 0.3);

        .summary-header {
            margin-bottom: .125rem;

            .summary-title {
                color: #4ab4ff;
                font-size: .175rem;
                font-weight: bold;
            }
        }

        .summary-stats {
            display: flex;
            gap: .25rem;

            .stat-item {
                display: flex;
                flex-direction: column;
                align-items: center;

                .stat-label {
                    color: #999;
                    font-size: .125rem;
                    margin-bottom: .0625rem;
                }

                .stat-value {
                    color: #4ab4ff;
                    font-size: .15rem;
                    font-weight: bold;
                }
            }
        }
    }

    .model-comparison {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: .25rem;
        flex: 1;

        .model-result-panel {
            display: flex;
            flex-direction: column;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: .0625rem;
            border: .025rem solid rgba(255, 255, 255, 0.1);
            overflow: hidden;

            .model-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: .125rem .15rem;
                background-color: rgba(74, 180, 255, 0.1);
                border-bottom: .025rem solid rgba(255, 255, 255, 0.1);

                .model-name {
                    color: #fff;
                    font-size: .15rem;
                    font-weight: bold;
                }

                .model-status {
                    font-size: .1rem;
                    padding: .03125rem .0625rem;
                    border-radius: .03125rem;

                    &.success {
                        color: #4ab4ff;
                        background-color: rgba(74, 180, 255, 0.2);
                    }

                    &.error {
                        color: #ff6b6b;
                        background-color: rgba(255, 107, 107, 0.2);
                    }
                }
            }

            .model-content {
                display: flex;
                flex-direction: column;
                flex: 1;
                padding: .125rem;

                .model-image-container {
                    position: relative;
                    margin-bottom: .125rem;
                    border-radius: .0625rem;
                    overflow: hidden;

                    .model-result-image {
                        width: 100%;
                        height: auto;
                        max-height: 2.5rem;
                        object-fit: contain;
                        background-color: rgba(0, 0, 0, 0.3);
                    }

                    .model-zoom-icon {
                        position: absolute;
                        top: .0625rem;
                        right: .0625rem;
                        width: .25rem;
                        height: .25rem;
                        background-color: rgba(0, 0, 0, 0.6);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: background-color 0.3s;

                        &:hover {
                            background-color: rgba(0, 0, 0, 0.8);
                        }
                    }
                }

                .model-detection-summary {
                    .detection-count {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: .0625rem;

                        .count-label {
                            color: #999;
                            font-size: .1rem;
                        }

                        .count-value {
                            color: #4ab4ff;
                            font-size: .1rem;
                            font-weight: bold;
                        }
                    }

                    .class-details {
                        .class-header {
                            color: #999;
                            font-size: .1rem;
                            margin-bottom: .0625rem;
                        }

                        .class-item {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: .03125rem;
                            padding: .03125rem .0625rem;
                            background-color: rgba(74, 180, 255, 0.1);
                            border-radius: .03125rem;

                            .class-name {
                                color: #fff;
                                font-size: .09375rem;
                            }

                            .class-count {
                                color: #4ab4ff;
                                font-size: .09375rem;
                                font-weight: bold;
                            }
                        }
                    }
                }

                .model-inference-log {
                    margin-top: .125rem;
                    border: .0125rem solid rgba(156, 172, 198, 0.2);
                    border-radius: .0625rem;
                    background: rgba(24, 26, 36, 0.5);

                    .log-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: .0625rem .125rem;
                        border-bottom: .0125rem solid rgba(156, 172, 198, 0.1);
                        cursor: pointer;

                        &:hover {
                            background-color: rgba(74, 180, 255, 0.1);
                        }

                        .log-title {
                            color: #9cacc6;
                            font-size: .1rem;
                        }

                        .q-btn {
                            transition: transform 0.3s ease;

                            &.rotated {
                                transform: rotate(180deg);
                            }
                        }
                    }

                    .log-content {
                        padding: .125rem;
                        max-height: 2.5rem;
                        overflow-y: auto;

                        .log-text {
                            color: #cea345;
                            font-size: .09375rem;
                            line-height: 1.4;
                            margin: 0;
                            white-space: pre-wrap;
                            word-wrap: break-word;
                            font-family: 'Courier New', monospace;
                        }
                    }
                }
            }

            .model-error {
                padding: .125rem;
                display: flex;
                align-items: center;
                justify-content: center;
                flex: 1;

                .error-message {
                    color: #ff6b6b;
                    font-size: .125rem;
                    text-align: center;
                }
            }
        }
    }
}
</style>
