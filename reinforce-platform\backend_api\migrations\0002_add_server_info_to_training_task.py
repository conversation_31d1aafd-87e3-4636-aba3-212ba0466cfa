# Generated migration for adding server info fields to TrainingTask

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('backend_api', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='trainingtask',
            name='server_ip',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='服务器IP'),
        ),
        migrations.AddField(
            model_name='trainingtask',
            name='server_port',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='服务器端口'),
        ),
        migrations.AddField(
            model_name='trainingtask',
            name='server_password',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='服务器密码'),
        ),
        migrations.AddField(
            model_name='trainingtask',
            name='server_info',
            field=models.JSONField(blank=True, default=dict, help_text='包含完整的服务器连接信息', verbose_name='服务器详细信息'),
        ),
    ]
