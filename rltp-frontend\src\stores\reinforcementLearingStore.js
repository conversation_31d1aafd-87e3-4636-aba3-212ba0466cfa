import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useReinforcementLearningStore = defineStore('reinforcementLearning', () => {
  // 当前任务ID
  const currentTaskId = ref(null)

  // 第一步数据 - 场景信息
  const stepOneData = ref({
    scenarios: [
      {
        scenario: {
          title: '城市反恐作战',
          status: '较难',
          environmenttype: '城市环境',
          info: '在复杂城市环境中执行任务，需要精确保护。',
          detailinfo: {
            detaillocation: '密集区',
            detailtime: '1小时',
            detailour: '我方：20人',
            detailenemy: '敌方：100人',
          },
          addtime: '2025 /08 /29',
        },
        scenariodetail: {
          status: '较难',
          type: '城市拦截作战',
          info: '执行城市拦截任务，阻止侵入城市。',
          detailinfo: [
            {
              type: 'Environment',
              info: '城市环境',
            },
            {
              type: 'Terrain',
              info: '山地地形',
            },
            {
              type: 'Weather',
              info: '晴天',
            },
          ],
        },
        forceComparison: [
          {
            type: 0,
            totalSum: 1,
            name: '炮兵营',
            fleets: [
              {
                name: '炮1',
                status: false,
                type: [
                  {
                    name: '70毫米炮弹',
                    count: 5,
                  },
                  {
                    name: '280毫米炮弹',
                    count: 5,
                  },
                  {
                    name: '150毫米炮弹',
                    count: 5,
                  },
                ],
              },
            ],
          },
          {
            type: 1,
            totalSum: 5,
            name: '时敏目标',
            fleets: [
              {
                name: '时敏目标1',
                count: 1,
              },
              {
                name: '时敏目标2',
                count: 1,
              },
              {
                name: '时敏目标3',
                count: 1,
              },
              {
                name: '时敏目标4',
                count: 1,
              },
              {
                name: '时敏目标5',
                count: 1,
              },
            ],
          },
        ],
        missionObjectives: [
          { title: '保护东路' },
          { title: '距离前方' },
          { title: '维护道路安全' },
          { title: '维护航道安全' },
          { title: '维护安全' },
        ],
        detailedInfo: {
          Ourforces: '',
          Enemyforces: '',
          TroopRatio: '',
          AddTime: '2025/08/29',
          SceneID: '100.0%',
        },
      },
      {
        scenario: {
          title: '空中拦截作战',
          status: '困难',
          environmenttype: '空中环境',
          info: '执行空中拦截任务，阻止敌机侵入领空',
          detailinfo: {
            detaillocation: '高楼密集区',
            detailtime: '2小时',
            detailour: '我方：14人',
            detailenemy: '敌方：200人',
          },
          addtime: '2025 /08 /29',
        },
        scenariodetail: {
          status: '困难',
          type: '空中拦截作战',
          info: '执行空中拦截任务，阻止侵入城市。',
          detailinfo: [
            {
              type: 'Environment',
              info: '城市环境',
            },
            {
              type: 'Terrain',
              info: '平原地形',
            },
            {
              type: 'Weather',
              info: '雨天',
            },
          ],
        },
        forceComparison: [
          {
            type: 0,
            totalSum: 2,
            name: '炮兵营',
            fleets: [
              {
                name: '炮1',
                status: false,
                type: [
                  {
                    name: '70毫米炮弹',
                    count: 5,
                  },
                  {
                    name: '280毫米炮弹',
                    count: 5,
                  },
                  {
                    name: '150毫米炮弹',
                    count: 5,
                  },
                ],
              },
              {
                name: '炮2',
                status: false,
                type: [
                  {
                    name: '70毫米炮弹',
                    count: 3,
                  },
                  {
                    name: '280毫米炮弹',
                    count: 3,
                  },
                  {
                    name: '150毫米炮弹',
                    count: 3,
                  },
                ],
              },
            ],
          },
          {
            type: 1,
            totalSum: 10,
            name: '时敏目标',
            fleets: [
              {
                name: '时敏目标1',
                count: 2,
              },
              {
                name: '时敏目标2',
                count: 2,
              },
              {
                name: '时敏目标3',
                count: 2,
              },
              {
                name: '时敏目标4',
                count: 2,
              },
              {
                name: '时敏目标5',
                count: 2,
              },
            ],
          },
        ],
        missionObjectives: [
          { title: '保护西路' },
          { title: '距离后方' },
          { title: '道路安全' },
          { title: '航道安全' },
          { title: '安全' },
        ],
        detailedInfo: {
          Ourforces: '',
          Enemyforces: '',
          TroopRatio: '',
          AddTime: '2025/08/29',
          SceneID: '90.0%',
        },
      },
    ],
  })

  // 第二步数据 - 模型参数
  const stepTwoData = ref({
    params: {
      epochs: '5',
      batchSize: '5',
      optimizer: 'Adam',
      learningRate: '1e-5',
      warmupSteps: '1000',
      logInterval: '100',
      evalInterval: '10',
    },
    resources: {
      npuCount: '1',
      cpuCount: '16',
    },
  })
  // 设置当前任务ID
  function setCurrentTaskId(taskId) {
    currentTaskId.value = taskId
  }

  // 清除当前任务ID
  function clearCurrentTaskId() {
    currentTaskId.value = null
  }

  // 更新第一步数据
  function updateStepOneData(data) {
    stepOneData.value = { ...stepOneData.value, ...data }
    console.log('强化学习-第一步数据已更新:', stepOneData.value)
  }

  function updateStepTwoData(data) {
    stepTwoData.value = { ...stepTwoData.value, ...data }
    console.log('强化学习-第二步数据已更新:', stepTwoData.value)
  }
  // 重置所有数据
  function resetAllData() {
    stepOneData.value = {
      scenarios: [
        {
          scenario: {
            title: '城市反恐作战',
            status: '较难',
            environmenttype: '城市环境',
            info: '在复杂城市环境中执行任务，需要精确保护。',
            detailinfo: {
              detaillocation: '密集区',
              detailtime: '1小时',
              detailour: '我方：20人',
              detailenemy: '敌方：100人',
            },
            addtime: '2025 /08 /29',
          },
          scenariodetail: {
            status: '较难',
            type: '城市拦截作战',
            info: '执行城市拦截任务，阻止侵入城市。',
            detailinfo: [
              {
                type: 'Environment',
                info: '城市环境',
              },
              {
                type: 'Terrain',
                info: '山地地形',
              },
              {
                type: 'Weather',
                info: '晴天',
              },
            ],
          },
          forceComparison: [
            {
              type: 0,
              totalSum: 1,
              name: '炮兵营',
              fleets: [
                {
                  name: '炮1',
                  status: false,
                  type: [
                    {
                      name: '70毫米炮弹',
                      count: 5,
                    },
                    {
                      name: '280毫米炮弹',
                      count: 5,
                    },
                    {
                      name: '150毫米炮弹',
                      count: 5,
                    },
                  ],
                },
              ],
            },
            {
              type: 1,
              totalSum: 5,
              name: '时敏目标',
              fleets: [
                {
                  name: '时敏目标1',
                  count: 1,
                },
                {
                  name: '时敏目标2',
                  count: 1,
                },
                {
                  name: '时敏目标3',
                  count: 1,
                },
                {
                  name: '时敏目标4',
                  count: 1,
                },
                {
                  name: '时敏目标5',
                  count: 1,
                },
              ],
            },
          ],
          missionObjectives: [
            { title: '保护东路' },
            { title: '距离前方' },
            { title: '维护道路安全' },
            { title: '维护航道安全' },
            { title: '维护安全' },
          ],
          detailedInfo: {
            Ourforces: '',
            Enemyforces: '',
            TroopRatio: '',
            AddTime: '2025/08/29',
            SceneID: '100.0%',
          },
        },
        {
          scenario: {
            title: '空中拦截作战',
            status: '困难',
            environmenttype: '空中环境',
            info: '执行空中拦截任务，阻止敌机侵入领空',
            detailinfo: {
              detaillocation: '高楼密集区',
              detailtime: '2小时',
              detailour: '我方：14人',
              detailenemy: '敌方：200人',
            },
            addtime: '2025 /08 /29',
          },
          scenariodetail: {
            status: '困难',
            type: '空中拦截作战',
            info: '执行空中拦截任务，阻止侵入城市。',
            detailinfo: [
              {
                type: 'Environment',
                info: '空中环境',
              },
              {
                type: 'Terrain',
                info: '平原地形',
              },
              {
                type: 'Weather',
                info: '雨天',
              },
            ],
          },
          forceComparison: [
            {
              type: 0,
              totalSum: 2,
              name: '炮兵营',
              fleets: [
                {
                  name: '炮1',
                  status: false,
                  type: [
                    {
                      name: '70毫米炮弹',
                      count: 5,
                    },
                    {
                      name: '280毫米炮弹',
                      count: 5,
                    },
                    {
                      name: '150毫米炮弹',
                      count: 5,
                    },
                  ],
                },
                {
                  name: '炮2',
                  status: false,
                  type: [
                    {
                      name: '70毫米炮弹',
                      count: 3,
                    },
                    {
                      name: '280毫米炮弹',
                      count: 3,
                    },
                    {
                      name: '150毫米炮弹',
                      count: 3,
                    },
                  ],
                },
              ],
            },
            {
              type: 1,
              totalSum: 10,
              name: '时敏目标',
              fleets: [
                {
                  name: '时敏目标1',
                  count: 2,
                },
                {
                  name: '时敏目标2',
                  count: 2,
                },
                {
                  name: '时敏目标3',
                  count: 2,
                },
                {
                  name: '时敏目标4',
                  count: 2,
                },
                {
                  name: '时敏目标5',
                  count: 2,
                },
              ],
            },
          ],
          missionObjectives: [
            { title: '保护西路' },
            { title: '距离后方' },
            { title: '道路安全' },
            { title: '航道安全' },
            { title: '安全' },
          ],
          detailedInfo: {
            Ourforces: '',
            Enemyforces: '',
            TroopRatio: '',
            AddTime: '2025/08/29',
            SceneID: '90.0%',
          },
        },
      ],
    },
      stepTwoData.value = {
        params: {
          epochs: '5',
          batchSize: '5',
          optimizer: 'Adam',
          learningRate: '1e-5',
          warmupSteps: '1000',
          logInterval: '100',
          evalInterval: '10',
        },
        resources: {
          npuCount: '1',
          cpuCount: '16',
        },
      }
    console.log('强化学习-所有数据已重置')
  }
  return {
    currentTaskId,
    stepOneData,
    stepTwoData,
    setCurrentTaskId,
    clearCurrentTaskId,
    updateStepOneData,
    updateStepTwoData,
    resetAllData,
  }
})
